{"name": "http", "type": "http", "displayName": "HTTP", "icon": "🌐", "group": "core", "category": ["builtin-popular", "integration"], "description": "Make HTTP requests to external APIs and services", "settings": {"authentication": {"name": "authentication", "displayName": "Authentication", "description": "Authentication settings for the request", "type": "object", "required": false, "order": 2, "properties": {"type": {"name": "type", "displayName": "Authentication Type", "description": "The type of authentication to use", "type": "options", "options": [{"name": "none", "value": "none"}, {"name": "basic_auth", "value": "basic_auth"}, {"name": "digest_auth", "value": "digest_auth"}, {"name": "api_key", "value": "api_key"}, {"name": "bearer_token", "value": "bearer_token"}, {"name": "oauth2", "value": "oauth2"}, {"name": "aws_sig_v4", "value": "aws_sig_v4"}, {"name": "ntlm_auth", "value": "ntlm_auth"}, {"name": "wsse_auth", "value": "wsse_auth"}], "default": "none", "required": true, "order": 1}, "credential": {"name": "credential", "displayName": "Credential", "description": "The credential to use for authentication", "type": "credential", "credentialTypes": ["basic_auth", "digest_auth", "api_key", "bearer_token", "oauth2", "aws_sig_v4", "ntlm_auth", "wsse_auth"], "required": false, "order": 2, "visibleIf": {"type": ["basic_auth", "digest_auth", "api_key", "bearer_token", "oauth2", "aws_sig_v4", "ntlm_auth", "wsse_auth"]}}}}, "retryConfig": {"name": "retryConfig", "displayName": "Retry Configuration", "description": "Configure retry behavior for failed requests", "type": "object", "required": false, "order": 2, "properties": {"maxRetries": {"name": "maxRetries", "displayName": "Max Retries", "description": "Maximum number of retries for a failed request. Set to 0 to disable retries.", "type": "number", "default": 3, "order": 1}, "initialInterval": {"name": "initialInterval", "displayName": "Initial Interval (ms)", "description": "Initial interval in milliseconds between retries.", "type": "number", "default": 1000, "order": 2}, "maxInterval": {"name": "maxInterval", "displayName": "<PERSON> (ms)", "description": "Maximum interval in milliseconds between retries.", "type": "number", "default": 60000, "order": 3}, "multiplier": {"name": "multiplier", "displayName": "Multiplier", "description": "Multiplier for the backoff interval.", "type": "number", "default": 2, "order": 4}, "maxElapsedTime": {"name": "maxElapsedTime", "displayName": "Max Elapsed Time (ms)", "description": "Maximum total time in milliseconds for all retries.", "type": "number", "default": 900000, "order": 5}}}}, "schemas": {"httpResponse": {"properties": {"statusCode": {"name": "statusCode", "displayName": "Status Code", "type": "number", "description": "The HTTP status code of the response", "order": 1}, "headers": {"name": "headers", "displayName": "Response Headers", "type": "object", "description": "The HTTP headers from the response", "order": 2}, "body": {"name": "body", "displayName": "Response Body", "type": ["object", "array", "string", "number", "boolean", "null"], "description": "The body of the response, parsed if JSON or XML", "order": 3}, "rawBody": {"name": "rawBody", "displayName": "Raw Response Body", "type": "string", "description": "The unparsed body of the response as a string", "order": 4}, "url": {"name": "url", "displayName": "Request URL", "type": "string", "description": "The final URL that was requested (after any redirects)", "order": 5}, "duration": {"name": "duration", "displayName": "Duration", "type": "number", "description": "The time taken for the request in milliseconds", "order": 6}, "pages": {"name": "pages", "displayName": "Pages", "type": "array", "description": "For paginated requests, a list of responses from each page.", "order": 7}, "allData": {"name": "allData", "displayName": "All Data", "type": "array", "description": "For paginated requests, a combined list of data items from all pages.", "order": 8}}}}, "credentials": {"basic_auth": {"name": "basic_auth", "displayName": "Basic Auth", "description": "Username and password for Basic Authentication", "order": 1, "properties": {"username": {"name": "username", "displayName": "Username", "description": "Username for Basic Authentication", "type": "text", "required": true, "order": 1}, "password": {"name": "password", "displayName": "Password", "description": "Password for Basic Authentication", "type": "password", "required": true, "order": 2}}}, "digest_auth": {"name": "digest_auth", "displayName": "Digest Auth", "description": "Username and password for Digest Authentication", "order": 2, "properties": {"username": {"name": "username", "displayName": "Username", "description": "Username for Digest Authentication", "type": "text", "required": true, "order": 1}, "password": {"name": "password", "displayName": "Password", "description": "Password for Digest Authentication", "type": "password", "required": true, "order": 2}}}, "api_key": {"name": "api_key", "displayName": "API Key", "description": "API Key for authentication", "order": 3, "properties": {"key": {"name": "key", "displayName": "API Key", "description": "The API key value", "type": "password", "required": true, "order": 1}, "addTo": {"name": "addTo", "displayName": "Add To", "description": "Where to add the API key", "type": "options", "options": [{"name": "header", "value": "header"}, {"name": "query", "value": "query"}], "default": "header", "required": true, "order": 2}, "keyName": {"name": "keyName", "displayName": "Key Name", "description": "The name to use for the API key (header name or query parameter name)", "type": "text", "default": "X-API-Key", "required": true, "order": 3}}}, "bearer_token": {"name": "bearer_token", "displayName": "<PERSON><PERSON>", "description": "Bearer token for OAuth or JWT authentication", "order": 4, "properties": {"token": {"name": "token", "displayName": "Token", "description": "The bearer token value", "type": "password", "required": true, "order": 1}}}, "oauth2": {"name": "oauth2", "displayName": "OAuth 2.0", "description": "OAuth 2.0 credentials for authentication", "order": 5, "properties": {"grantType": {"name": "grantType", "displayName": "Grant Type", "description": "The OAuth 2.0 grant type to use for authentication", "type": "options", "options": [{"name": "Client Credentials", "value": "client_credentials"}, {"name": "Authorization Code", "value": "authorization_code"}, {"name": "Password", "value": "password"}, {"name": "Implicit", "value": "implicit"}, {"name": "Refresh <PERSON>", "value": "refresh_token"}], "default": "client_credentials", "required": true, "order": 1}, "clientId": {"name": "clientId", "displayName": "Client ID", "description": "OAuth 2.0 client ID", "type": "text", "required": true, "order": 2}, "clientSecret": {"name": "clientSecret", "displayName": "Client Secret", "description": "OAuth 2.0 client secret", "type": "password", "required": true, "order": 3, "visibleIf": {"grantType": ["client_credentials", "authorization_code", "password", "refresh_token"]}}, "accessToken": {"name": "accessToken", "displayName": "Access Token", "description": "OAuth 2.0 access token (if you already have one)", "type": "password", "required": false, "order": 4}, "refreshToken": {"name": "refreshToken", "displayName": "Refresh <PERSON>", "description": "OAuth 2.0 refresh token (required for refresh_token grant type)", "type": "password", "required": false, "order": 5, "visibleIf": {"grantType": ["authorization_code", "password", "refresh_token"]}}, "tokenEndpoint": {"name": "tokenEndpoint", "displayName": "Token Endpoint", "description": "The endpoint URL for obtaining tokens", "type": "text", "required": false, "order": 6, "visibleIf": {"grantType": ["client_credentials", "authorization_code", "password", "refresh_token"]}}}}}, "actions": {"get": {"name": "get", "displayName": "GET", "description": "Send a GET request", "order": 1, "properties": {"url": {"name": "url", "displayName": "URL", "description": "The URL to send the GET request to. Must start with http:// or https://", "type": "text", "required": true, "order": 1}, "queryParams": {"name": "queryParams", "displayName": "Query Parameters", "description": "Parameters to add to the URL query string", "type": "keyvalue", "required": false, "order": 2}, "headers": {"name": "headers", "displayName": "Headers", "description": "HTTP headers to include in the request", "type": "keyvalue", "required": false, "order": 3}, "timeout": {"name": "timeout", "displayName": "Timeout (ms)", "description": "Request timeout in milliseconds. 0 means no timeout.", "type": "number", "default": 10000, "order": 4}, "followRedirects": {"name": "followRedirects", "displayName": "Follow Redirects", "description": "Whether to follow HTTP redirects (3xx responses)", "type": "boolean", "default": true, "order": 5}, "pagination": {"name": "pagination", "displayName": "Enable Pagination", "description": "Enable automatic handling of paginated responses. Only for GET requests.", "type": "boolean", "default": false, "order": 6}, "paginationType": {"name": "paginationType", "displayName": "Pagination Type", "description": "The type of pagination to use.", "type": "options", "options": [{"name": "Page Number", "value": "page_number"}, {"name": "Page Offset", "value": "page_offset"}, {"name": "<PERSON><PERSON><PERSON>", "value": "cursor"}], "default": "page_number", "order": 7, "visibleIf": {"pagination": [true]}}, "pageParamName": {"name": "pageParamName", "displayName": "Page/Offset Parameter Name", "description": "Name of the query parameter for page number or offset.", "type": "text", "default": "page", "order": 8, "visibleIf": {"paginationType": ["page_number", "page_offset"]}}, "pageSizeParamName": {"name": "pageSizeParamName", "displayName": "Page Size Parameter Name", "description": "Name of the query parameter for page size.", "type": "text", "default": "limit", "order": 9, "visibleIf": {"pagination": [true]}}, "pageSize": {"name": "pageSize", "displayName": "<PERSON>", "description": "Number of items per page.", "type": "number", "default": 10, "order": 10, "visibleIf": {"pagination": [true]}}, "startPage": {"name": "startPage", "displayName": "Start Page", "description": "The page number to start from.", "type": "number", "default": 1, "order": 11, "visibleIf": {"paginationType": ["page_number"]}}, "startOffset": {"name": "startOffset", "displayName": "Start Offset", "description": "The offset to start from.", "type": "number", "default": 0, "order": 12, "visibleIf": {"paginationType": ["page_offset"]}}, "cursorParamName": {"name": "cursorParamName", "displayName": "Cursor Parameter Name", "description": "Name of the query parameter for the cursor.", "type": "text", "default": "cursor", "order": 13, "visibleIf": {"paginationType": ["cursor"]}}, "cursorPath": {"name": "cursor<PERSON><PERSON>", "displayName": "Cursor Path in Response", "description": "JSONPath to the next cursor value in the response body.", "type": "text", "order": 14, "visibleIf": {"paginationType": ["cursor"]}}, "maxPages": {"name": "maxPages", "displayName": "<PERSON> to Fetch", "description": "Maximum number of pages to fetch. 0 for no limit.", "type": "number", "default": 5, "order": 15, "visibleIf": {"pagination": [true]}}, "hasMorePath": {"name": "has<PERSON><PERSON><PERSON><PERSON>", "displayName": "'Has More' Path in Response", "description": "JSONPath to a boolean indicating if there are more pages.", "type": "text", "order": 16, "visibleIf": {"pagination": [true]}}, "totalPath": {"name": "totalPath", "displayName": "Total Items Path in Response", "description": "JSONPath to the total number of items.", "type": "text", "order": 17, "visibleIf": {"pagination": [true]}}, "dataPath": {"name": "dataPath", "displayName": "Data Path in Response", "description": "JSONPath to the array of data in the response.", "type": "text", "default": "data", "order": 18, "visibleIf": {"pagination": [true]}}}, "output": {"$ref": "#/schemas/httpResponse"}}, "post": {"name": "post", "displayName": "POST", "description": "Send a POST request", "order": 2, "properties": {"url": {"name": "url", "displayName": "URL", "description": "The URL to send the POST request to. Must start with http:// or https://", "type": "text", "required": true, "order": 1}, "queryParams": {"name": "queryParams", "displayName": "Query Parameters", "description": "Parameters to add to the URL query string", "type": "keyvalue", "required": false, "order": 2}, "headers": {"name": "headers", "displayName": "Headers", "description": "HTTP headers to include in the request", "type": "keyvalue", "required": false, "order": 3}, "body": {"name": "body", "displayName": "Body", "description": "Request body. Can be a JSON string, form data, or raw text.", "type": "textarea", "required": false, "order": 4}, "timeout": {"name": "timeout", "displayName": "Timeout (ms)", "description": "Request timeout in milliseconds. 0 means no timeout.", "type": "number", "default": 10000, "order": 5}, "followRedirects": {"name": "followRedirects", "displayName": "Follow Redirects", "description": "Whether to follow HTTP redirects (3xx responses)", "type": "boolean", "default": true, "order": 6}}, "output": {"$ref": "#/schemas/httpResponse"}}, "put": {"name": "put", "displayName": "PUT", "description": "Send a PUT request", "order": 3, "properties": {"url": {"name": "url", "displayName": "URL", "description": "The URL to send the PUT request to. Must start with http:// or https://", "type": "text", "required": true, "order": 1}, "queryParams": {"name": "queryParams", "displayName": "Query Parameters", "description": "Parameters to add to the URL query string", "type": "keyvalue", "required": false, "order": 2}, "headers": {"name": "headers", "displayName": "Headers", "description": "HTTP headers to include in the request", "type": "keyvalue", "required": false, "order": 3}, "body": {"name": "body", "displayName": "Body", "description": "Request body. Can be a JSON string, form data, or raw text.", "type": "textarea", "required": false, "order": 4}, "timeout": {"name": "timeout", "displayName": "Timeout (ms)", "description": "Request timeout in milliseconds. 0 means no timeout.", "type": "number", "default": 10000, "order": 5}, "followRedirects": {"name": "followRedirects", "displayName": "Follow Redirects", "description": "Whether to follow HTTP redirects (3xx responses)", "type": "boolean", "default": true, "order": 6}}, "output": {"$ref": "#/schemas/httpResponse"}}, "patch": {"name": "patch", "displayName": "PATCH", "description": "Send a PATCH request", "order": 4, "properties": {"url": {"name": "url", "displayName": "URL", "description": "The URL to send the PATCH request to. Must start with http:// or https://", "type": "text", "required": true, "order": 1}, "queryParams": {"name": "queryParams", "displayName": "Query Parameters", "description": "Parameters to add to the URL query string", "type": "keyvalue", "required": false, "order": 2}, "headers": {"name": "headers", "displayName": "Headers", "description": "HTTP headers to include in the request", "type": "keyvalue", "required": false, "order": 3}, "body": {"name": "body", "displayName": "Body", "description": "Request body. Can be a JSON string, form data, or raw text.", "type": "textarea", "required": false, "order": 4}, "timeout": {"name": "timeout", "displayName": "Timeout (ms)", "description": "Request timeout in milliseconds. 0 means no timeout.", "type": "number", "default": 10000, "order": 5}, "followRedirects": {"name": "followRedirects", "displayName": "Follow Redirects", "description": "Whether to follow HTTP redirects (3xx responses)", "type": "boolean", "default": true, "order": 6}}, "output": {"$ref": "#/schemas/httpResponse"}}, "delete": {"name": "delete", "displayName": "DELETE", "description": "Send a DELETE request", "order": 5, "properties": {"url": {"name": "url", "displayName": "URL", "description": "The URL to send the DELETE request to. Must start with http:// or https://", "type": "text", "required": true, "order": 1}, "queryParams": {"name": "queryParams", "displayName": "Query Parameters", "description": "Parameters to add to the URL query string", "type": "keyvalue", "required": false, "order": 2}, "headers": {"name": "headers", "displayName": "Headers", "description": "HTTP headers to include in the request", "type": "keyvalue", "required": false, "order": 3}, "timeout": {"name": "timeout", "displayName": "Timeout (ms)", "description": "Request timeout in milliseconds. 0 means no timeout.", "type": "number", "default": 10000, "order": 4}, "followRedirects": {"name": "followRedirects", "displayName": "Follow Redirects", "description": "Whether to follow HTTP redirects (3xx responses)", "type": "boolean", "default": true, "order": 5}}, "output": {"$ref": "#/schemas/httpResponse"}}, "options": {"name": "options", "displayName": "OPTIONS", "description": "Send an OPTIONS request", "order": 6, "properties": {"url": {"name": "url", "displayName": "URL", "description": "The URL to send the OPTIONS request to. Must start with http:// or https://", "type": "text", "required": true, "order": 1}, "queryParams": {"name": "queryParams", "displayName": "Query Parameters", "description": "Parameters to add to the URL query string", "type": "keyvalue", "required": false, "order": 2}, "headers": {"name": "headers", "displayName": "Headers", "description": "HTTP headers to include in the request", "type": "keyvalue", "required": false, "order": 3}, "timeout": {"name": "timeout", "displayName": "Timeout (ms)", "description": "Request timeout in milliseconds. 0 means no timeout.", "type": "number", "default": 10000, "order": 4}, "followRedirects": {"name": "followRedirects", "displayName": "Follow Redirects", "description": "Whether to follow HTTP redirects (3xx responses)", "type": "boolean", "default": true, "order": 5}}, "output": {"$ref": "#/schemas/httpResponse"}}}}