{"name": "openai", "displayName": "OpenAI", "icon": "🤖", "group": "AI", "category": ["ai-tools"], "description": "OpenAI API", "version": "1.0.0", "type": "node", "settings": {"credential": {"name": "credential", "displayName": "Credential", "type": "credential", "allowedCredentials": ["system.default", "openai.api_key"], "required": true, "description": "Credential to use", "order": 1}, "openai_base_url": {"name": "openai_base_url", "displayName": "OpenAI Base URL", "description": "The base URL for the OpenAI API (leave empty for default)", "type": "url", "required": false, "default": "https://api.openai.com/v1"}, "simplify_response": {"name": "simplify_response", "displayName": "Simplify Response", "description": "Simplify the response from the model", "type": "boolean", "required": false, "default": true}}, "schemas": {}, "credentials": {"openai.api_key": {"name": "openai.api_key", "displayName": "OpenAI API Key", "description": "API Key for authenticating with OpenAI", "properties": [{"name": "openai_api_key", "displayName": "OpenAI API Key", "description": "Your OpenAI API key", "type": "password", "required": true}]}}, "triggers": {}, "actions": {"text_chat": {"name": "text_chat", "displayName": "Text Chat", "description": "Chat with OpenAI", "tags": ["text"], "properties": {"modelId": {"name": "modelId", "displayName": "Model ID", "description": "The model ID to use", "type": "options", "required": true, "default": "gpt-4.1-mini", "options": [{"value": "gpt-4.1-mini", "label": "GPT-4.1-Mini"}, {"value": "gpt-4.1-nano", "label": "GPT-4.1-<PERSON><PERSON>"}, {"value": "gpt-4.1-micro", "label": "GPT-4.1-Micro"}]}, "messages": {"name": "messages", "displayName": "Messages", "description": "The messages to send to the model", "type": "array", "required": true, "items": {"type": "object", "properties": {"role": {"name": "role", "displayName": "Role", "description": "The role of the message", "type": "options", "required": true, "options": [{"name": "user", "value": "user"}, {"name": "assistant", "value": "assistant"}, {"name": "system", "value": "system"}]}, "content": {"name": "content", "displayName": "Content", "description": "The content of the message", "type": "string", "required": true}}}}, "temperature": {"name": "temperature", "displayName": "Temperature", "description": "The temperature to use", "type": "number", "required": false, "default": 0.5}, "top_p": {"name": "top_p", "displayName": "Top P", "description": "The top P to use", "type": "number", "required": false, "default": 0.5}, "frequency_penalty": {"name": "frequency_penalty", "displayName": "Frequency Penalty", "description": "The frequency penalty to use", "type": "number", "required": false, "default": 0.0}, "presence_penalty": {"name": "presence_penalty", "displayName": "Presence Penalty", "description": "The presence penalty to use", "type": "number", "required": false, "default": 0.0}, "max_tokens": {"name": "max_tokens", "displayName": "<PERSON>", "description": "The maximum number of tokens to generate", "type": "number", "required": false, "default": 256}, "stop": {"name": "stop", "displayName": "Stop", "description": "The stop sequence to use", "type": "string", "required": false, "default": ""}}}, "classify_text_for_violations": {"name": "classify_text_for_violations", "displayName": "Classify Text for Violations", "description": "Identify and flag content that might be harmful using OpenAI's moderation API", "tags": ["moderation", "safety", "text"], "properties": {"text_input": {"name": "text_input", "displayName": "Text Input", "description": "Enter text to classify if it violates the moderation policy", "type": "string", "required": true}, "options": {"name": "options", "displayName": "Options", "description": "Additional options for text moderation", "type": "object", "required": false, "properties": {"use_stable_model": {"name": "use_stable_model", "displayName": "Use Stable Model", "description": "Use the stable version of the model instead of the latest version (accuracy may be slightly lower)", "type": "boolean", "required": false, "default": false}}}}}, "generate_image": {"name": "generate_image", "displayName": "Generate Image", "description": "Generate an image from a text prompt using OpenAI's image models (DALL-E)", "tags": ["image", "generation", "dall-e"], "properties": {"prompt": {"name": "prompt", "displayName": "Prompt", "description": "Text description of the desired image(s)", "type": "string", "required": true}, "model": {"name": "model", "displayName": "Model", "description": "The model to use for image generation", "type": "options", "required": true, "default": "dall-e-3", "options": [{"value": "dall-e-2", "label": "DALL-E 2"}, {"value": "dall-e-3", "label": "DALL-E 3"}]}, "quality": {"name": "quality", "displayName": "Quality", "description": "Image quality (HD only for DALL-E 3)", "type": "options", "required": false, "default": "standard", "options": [{"value": "standard", "label": "Standard"}, {"value": "hd", "label": "HD"}]}, "resolution": {"name": "resolution", "displayName": "Resolution", "description": "Resolution of the generated image", "type": "options", "required": false, "default": "1024x1024", "options": [{"value": "1024x1024", "label": "1024x1024"}, {"value": "1792x1024", "label": "1792x1024"}, {"value": "1024x1792", "label": "1024x1792"}]}, "style": {"name": "style", "displayName": "Style", "description": "Style of the generated image (DALL-E 3 only)", "type": "options", "required": false, "default": "vivid", "options": [{"value": "vivid", "label": "Vivid"}, {"value": "natural", "label": "Natural"}]}, "response_format": {"name": "response_format", "displayName": "Respond with image URL(s)", "description": "Whether to return image URLs or binary data", "type": "options", "required": false, "default": "url", "options": [{"value": "url", "label": "Image URL(s)"}, {"value": "binary", "label": "Binary File(s)"}]}, "output_field": {"name": "output_field", "displayName": "Put Output in Field", "description": "Name of the output field for binary data (if not using URLs)", "type": "string", "required": false, "default": "data"}, "num_images": {"name": "num_images", "displayName": "Number of Images", "description": "Number of images to generate", "type": "number", "required": false, "default": 1}}}, "analyze_image": {"name": "analyze_image", "displayName": "Analyze Image", "description": "Analyze an image and answer questions about it using OpenAI's vision models", "tags": ["image", "vision", "analyze"], "properties": {"question": {"name": "question", "displayName": "Text Input", "description": "Ask a question about the image", "type": "string", "required": true}, "model": {"name": "model", "displayName": "Model", "description": "The model to use for image analysis", "type": "options", "required": true, "default": "gpt-4-vision-preview", "options": [{"value": "gpt-4-vision-preview", "label": "GPT-4 Vision Preview"}]}, "input_type": {"name": "input_type", "displayName": "Input Type", "description": "How to input the image(s)", "type": "options", "required": true, "default": "url", "options": [{"value": "url", "label": "Image URL(s)"}, {"value": "binary", "label": "Binary File(s)"}]}, "image_urls": {"name": "image_urls", "displayName": "Image URL(s)", "description": "Comma-separated list of image URLs to analyze (if using URL input)", "type": "string", "required": false}, "binary_field": {"name": "binary_field", "displayName": "Input Data Field Name", "description": "Name of the binary property containing the image(s) (if using binary input)", "type": "string", "required": false}, "detail": {"name": "detail", "displayName": "Detail", "description": "Balance between response time and token usage", "type": "options", "required": false, "default": "auto", "options": [{"value": "auto", "label": "Auto"}, {"value": "low", "label": "Low"}, {"value": "high", "label": "High"}]}, "max_tokens": {"name": "max_tokens", "displayName": "Length of Description (<PERSON>)", "description": "Maximum number of tokens in the description", "type": "number", "required": false, "default": 300}}}}}