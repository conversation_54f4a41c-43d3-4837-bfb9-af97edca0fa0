{"name": "deca-kb", "displayName": "DECA Knowledge Base", "icon": "📚", "group": "deca", "category": ["deca-cloud-tools"], "description": "Interact with DECA Knowledge Base API to manage folders, knowledge bases, articles, documents, and perform searches.", "version": "1.0.0", "type": "node", "settings": {"properties": {"credential": {"name": "credential", "displayName": "Credential", "type": "credential", "credentialTypes": ["deca.kb.api_key"], "required": true, "description": "Credential to use for DECA KB API", "order": 1}}}, "schemas": {"properties": {"articleContent": {"name": "articleContent", "displayName": "Article Content", "description": "Article content structure", "properties": {"title": {"name": "title", "displayName": "Title", "type": "string", "required": true, "description": "Article title"}, "content": {"name": "content", "displayName": "Content", "type": "string", "required": true, "description": "Article content (max 5000 chars)"}, "contentRaw": {"name": "contentRaw", "displayName": "Raw Content", "type": "string", "required": true, "description": "Article raw content (max 50000 chars)"}, "keywords": {"name": "keywords", "displayName": "Keywords", "type": "array", "description": "Article keywords"}}}, "customDataInput": {"name": "customDataInput", "displayName": "Custom Data Input", "description": "Custom data input structure", "properties": {"title": {"name": "title", "displayName": "Title", "type": "string", "required": true, "description": "Custom data title"}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Custom data description"}, "dataType": {"name": "dataType", "displayName": "Data Type", "type": "string", "required": true, "description": "Type of custom data (text or array)"}, "value": {"name": "value", "displayName": "Value", "type": "object", "description": "Value content - supports both 'text' (string) and 'array' (array of strings) based on dataType", "oneOf": [{"type": "string", "description": "String value when dataType is 'text'"}, {"type": "array", "items": {"type": "string"}, "description": "Array of strings when dataType is 'array'"}]}}}, "articleResponse": {"name": "articleResponse", "displayName": "Article Response", "description": "Article response data", "properties": {"id": {"name": "id", "displayName": "Article ID", "type": "string", "description": "Unique identifier for the article"}, "entityType": {"name": "entityType", "displayName": "Entity Type", "type": "string", "description": "Type of entity (article or article-shortcut)"}, "baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "string", "description": "Knowledge base ID"}, "datasourceId": {"name": "datasourceId", "displayName": "Datasource ID", "type": "string", "description": "Datasource ID"}, "title": {"name": "title", "displayName": "Title", "type": "string", "description": "Article title"}, "content": {"name": "content", "displayName": "Content", "type": "string", "description": "Article content"}, "contentRaw": {"name": "contentRaw", "displayName": "Raw Content", "type": "string", "description": "Article raw content"}, "status": {"name": "status", "displayName": "Status", "type": "string", "description": "Article status (draft, published, etc.)"}, "version": {"name": "version", "displayName": "Version", "type": "number", "description": "Article version"}, "keywords": {"name": "keywords", "displayName": "Keywords", "type": "array", "description": "Article keywords"}, "customData": {"name": "customData", "displayName": "Custom Data", "type": "array", "description": "Custom data fields"}, "relatedArticles": {"name": "relatedArticles", "displayName": "Related Articles", "type": "array", "description": "Related articles"}, "isShortcut": {"name": "isShortcut", "displayName": "Is Shortcut", "type": "boolean", "description": "Whether this article is a shortcut"}, "originArticleId": {"name": "originArticleId", "displayName": "Origin Article ID", "type": "string", "description": "Original article ID if this is a shortcut"}, "shortcutArticleIds": {"name": "shortcutArticleIds", "displayName": "Shortcut Article IDs", "type": "array", "description": "List of shortcut article IDs"}, "likeCount": {"name": "likeCount", "displayName": "Like Count", "type": "number", "description": "Number of likes"}, "dislikeCount": {"name": "dislikeCount", "displayName": "Dislike Count", "type": "number", "description": "Number of dislikes"}, "viewCount": {"name": "viewCount", "displayName": "View Count", "type": "number", "description": "Number of views"}, "createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Creation timestamp"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Last update timestamp"}, "createdBy": {"name": "created<PERSON>y", "displayName": "Created By", "type": ["string", "object"], "description": "User who created the article"}, "updatedBy": {"name": "updatedBy", "displayName": "Updated By", "type": "string", "description": "User who last updated the article"}}}, "kbResponse": {"name": "kbResponse", "displayName": "Knowledge Base Response", "description": "Knowledge base response data", "properties": {"id": {"name": "id", "displayName": "Knowledge Base ID", "type": "string", "description": "Unique identifier for the knowledge base"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Knowledge base name"}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Knowledge base description"}, "baseType": {"name": "baseType", "displayName": "Base Type", "type": "string", "description": "Type of knowledge base (document, article, from_datasource)"}, "accessLevel": {"name": "accessLevel", "displayName": "Access Level", "type": "string", "description": "Access level (private, public)"}, "parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "string", "description": "Parent directory ID"}, "datasourceId": {"name": "datasourceId", "displayName": "Datasource ID", "type": "string", "description": "Datasource ID"}, "parentDirBreadcrumb": {"name": "parentDirBreadcrumb", "displayName": "Parent Directory Breadcrumb", "type": "string", "description": "Path by name (deprecated)"}, "parentDirBreadcrumbArray": {"name": "parentDirBreadcrumbArray", "displayName": "Parent Directory Breadcrumb Array", "type": "array", "description": "Path by name array"}, "parentDirPath": {"name": "parent<PERSON>ir<PERSON><PERSON>", "displayName": "Parent Directory Path", "type": "string", "description": "Path by ULID"}, "processType": {"name": "processType", "displayName": "Process Type", "type": "string", "description": "Knowledge base processing type"}, "createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Creation timestamp"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Last update timestamp"}, "createdBy": {"name": "created<PERSON>y", "displayName": "Created By", "type": "string", "description": "User who created the knowledge base"}, "updatedBy": {"name": "updatedBy", "displayName": "Updated By", "type": "string", "description": "User who last updated the knowledge base"}}}, "folderResponse": {"name": "folderResponse", "displayName": "Folder Response", "description": "Folder response data", "properties": {"id": {"name": "id", "displayName": "Folder ID", "type": "string", "description": "Unique identifier for the folder"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Folder name"}, "path": {"name": "path", "displayName": "Path", "type": "string", "description": "Full path of the folder by ULID"}, "parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "string", "description": "Parent directory ID"}, "count": {"name": "count", "displayName": "Count", "type": "number", "description": "Count of child items (deprecated)"}, "childKbCount": {"name": "childKbCount", "displayName": "Child Knowledge Base Count", "type": "number", "description": "Count of child knowledge bases"}, "childFolderCount": {"name": "childFolderCount", "displayName": "Child Folder Count", "type": "number", "description": "Count of child folders"}, "breadcrumb": {"name": "breadcrumb", "displayName": "Breadcrumb", "type": "string", "description": "Path by name (deprecated)"}, "breadcrumbArray": {"name": "breadcrumbArray", "displayName": "Breadcrumb Array", "type": "array", "description": "Path by name array"}, "createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Creation timestamp"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Last update timestamp"}, "createdBy": {"name": "created<PERSON>y", "displayName": "Created By", "type": "string", "description": "User who created the folder"}, "updatedBy": {"name": "updatedBy", "displayName": "Updated By", "type": "string", "description": "User who last updated the folder"}}}, "documentResponse": {"name": "documentResponse", "displayName": "Document Response", "description": "Document response data", "properties": {"id": {"name": "id", "displayName": "Document ID", "type": "string", "description": "Unique identifier for the document"}, "metadata": {"name": "metadata", "displayName": "<PERSON><PERSON><PERSON>", "type": "object", "description": "Document metadata"}, "type": {"name": "type", "displayName": "Type", "type": "string", "description": "Document type"}, "folderId": {"name": "folderId", "displayName": "Folder ID", "type": "string", "description": "Folder ID (deprecated)"}, "parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "string", "description": "Parent directory ID"}, "parentDirBreadcrumb": {"name": "parentDirBreadcrumb", "displayName": "Parent Directory Breadcrumb", "type": "string", "description": "Path by name (deprecated)"}, "parentDirBreadcrumbArray": {"name": "parentDirBreadcrumbArray", "displayName": "Parent Directory Breadcrumb Array", "type": "array", "description": "Path by name array"}, "parentDirPath": {"name": "parent<PERSON>ir<PERSON><PERSON>", "displayName": "Parent Directory Path", "type": "string", "description": "Path by ULID"}, "accessLevel": {"name": "accessLevel", "displayName": "Access Level", "type": "string", "description": "Document access level (private, public)"}, "createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Creation timestamp"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Last update timestamp"}}}, "listResponse": {"name": "listResponse", "displayName": "List Response", "description": "Generic list response structure", "properties": {"data": {"name": "data", "displayName": "Data", "type": "array", "description": "List of items"}, "cursor": {"name": "cursor", "displayName": "<PERSON><PERSON><PERSON>", "type": "string", "description": "Pagination cursor"}, "hasMore": {"name": "hasMore", "displayName": "Has <PERSON>", "type": "boolean", "description": "Whether there are more items"}, "total": {"name": "total", "displayName": "Total", "type": "number", "description": "Total number of items"}}}, "searchResponse": {"name": "searchResponse", "displayName": "Search Response", "description": "Search results data", "properties": {"results": {"name": "results", "displayName": "Results", "type": "array", "description": "Search results"}, "query": {"name": "query", "displayName": "Query", "type": "string", "description": "Search query"}, "total": {"name": "total", "displayName": "Total", "type": "number", "description": "Total number of results"}, "took": {"name": "took", "displayName": "Took", "type": "number", "description": "Time taken for search in milliseconds"}}}, "askResponse": {"name": "askResponse", "displayName": "Ask Response", "description": "Ask results data", "properties": {"answer": {"name": "answer", "displayName": "Answer", "type": "string", "description": "Generated answer"}, "type": {"name": "type", "displayName": "Type", "type": "string", "description": "Response type (text or html)"}, "references": {"name": "references", "displayName": "References", "type": "array", "description": "Reference sources"}, "debugInfo": {"name": "debugInfo", "displayName": "Debug Info", "type": "array", "description": "Debug information"}}}, "customAskResponse": {"name": "customAskResponse", "displayName": "Custom Ask Response", "description": "Custom Ask results data", "properties": {"answer": {"name": "answer", "displayName": "Answer", "type": "string", "description": "Generated answer"}, "type": {"name": "type", "displayName": "Type", "type": "string", "description": "Response type (text or html)"}, "references": {"name": "references", "displayName": "References", "type": "array", "description": "Reference sources"}}}, "agentResponse": {"name": "agentResponse", "displayName": "Agent Response", "description": "Agent results data", "properties": {"answer": {"name": "answer", "displayName": "Answer", "type": "string", "description": "Generated answer"}, "type": {"name": "type", "displayName": "Type", "type": "string", "description": "Response type (text or html)"}, "references": {"name": "references", "displayName": "References", "type": "array", "description": "Reference sources"}}}, "deleteResponse": {"name": "deleteResponse", "displayName": "Delete Response", "description": "Delete operation response", "properties": {"message": {"name": "message", "displayName": "Message", "type": "string", "description": "Success message"}, "id": {"name": "id", "displayName": "ID", "type": "string", "description": "ID of deleted item"}}}, "documentDownloadLinkResponse": {"name": "documentDownloadLinkResponse", "displayName": "Document Download Link Response", "description": "Document download link response", "properties": {"downloadUrl": {"name": "downloadUrl", "displayName": "Download URL", "type": "string", "description": "Download URL for the document"}, "downloadUrlExpires": {"name": "downloadUrlExpires", "displayName": "Download URL Expires", "type": "string", "description": "Expiration time for the download URL"}}}}}, "credentials": {"deca.kb.api_key": {"name": "deca.kb.api_key", "displayName": "DECA KB API Key", "description": "API Key for authenticating with DECA Knowledge Base", "properties": [{"name": "api_key", "displayName": "API Key", "description": "Your DECA Knowledge Base API key", "type": "password", "required": true, "order": 1}, {"name": "base_url", "displayName": "Base URL", "description": "The base URL for the DECA KB API", "type": "text", "required": false, "default": "https://api.deca-kb.com", "order": 2}], "order": 1}}, "triggers": {"article_created": {"name": "article_created", "displayName": "Article Created", "description": "Triggered when a new article is created in the knowledge base", "data": {"$ref": "#/schemas/properties/articleResponse"}, "order": 1}, "article_updated": {"name": "article_updated", "displayName": "Article Updated", "description": "Triggered when an article is updated in the knowledge base", "data": {"$ref": "#/schemas/properties/articleResponse"}, "order": 2}, "kb_created": {"name": "kb_created", "displayName": "Knowledge Base Created", "description": "Triggered when a new knowledge base is created", "data": {"$ref": "#/schemas/properties/kbResponse"}, "order": 3}}, "actions": {"folder_list": {"name": "folder_list", "displayName": "List Folders", "description": "Retrieve a list of folders", "properties": {"parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "text", "description": "Parent directory ID (default: /root)", "required": false, "order": 1}, "depth": {"name": "depth", "displayName": "De<PERSON><PERSON>", "type": "number", "description": "Maximum depth to traverse (max: 5, default: 2)", "required": false, "order": 2}, "take": {"name": "take", "displayName": "Take", "type": "number", "description": "Number of items to take (max: 200, default: 100)", "required": false, "order": 3}}, "data": {"$ref": "#/schemas/listResponse"}, "order": 1}, "folder_create": {"name": "folder_create", "displayName": "Create Folder", "description": "Create a new folder", "properties": {"name": {"name": "name", "displayName": "Name", "type": "text", "required": true, "description": "Folder name", "order": 1}, "parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "text", "description": "Parent directory ID (default: /root)", "required": false, "order": 2}}, "data": {"$ref": "#/schemas/folderResponse"}, "order": 2}, "kb_list": {"name": "kb_list", "displayName": "List Knowledge Bases", "description": "Retrieve a list of knowledge bases", "properties": {"parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "text", "description": "Parent directory ID (default: /root)", "required": false, "order": 1}, "cursor": {"name": "cursor", "displayName": "<PERSON><PERSON><PERSON>", "type": "text", "description": "Pagination cursor", "required": false, "order": 2}, "take": {"name": "take", "displayName": "Take", "type": "number", "description": "Number of items to take (max: 100, default: 10)", "required": false, "order": 3}, "direction": {"name": "direction", "displayName": "Direction", "type": "options", "options": [{"name": "Forward", "value": "forward"}, {"name": "Backward", "value": "backward"}], "description": "Pagination direction", "required": false, "order": 4}}, "data": {"$ref": "#/schemas/listResponse"}, "order": 3}, "kb_create": {"name": "kb_create", "displayName": "Create Knowledge Base", "description": "Create a new knowledge base", "properties": {"name": {"name": "name", "displayName": "Name", "type": "text", "required": true, "description": "Knowledge base name", "order": 1}, "description": {"name": "description", "displayName": "Description", "type": "text", "description": "Knowledge base description", "required": false, "order": 2}, "accessLevel": {"name": "accessLevel", "displayName": "Access Level", "type": "options", "required": true, "options": [{"name": "Private", "value": "private"}, {"name": "Public", "value": "public"}], "description": "Access level", "order": 3}, "baseType": {"name": "baseType", "displayName": "Base Type", "type": "options", "required": true, "options": [{"name": "Document", "value": "document"}, {"name": "Article", "value": "article"}], "description": "Type of knowledge base", "order": 4}, "parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "text", "description": "Parent directory ID (default: /root)", "required": false, "order": 5}}, "data": {"$ref": "#/schemas/kbResponse"}, "order": 4}, "article_list": {"name": "article_list", "displayName": "List Articles", "description": "Retrieve a list of articles", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "description": "Knowledge base ID to list articles from", "required": false, "order": 1}, "status": {"name": "status", "displayName": "Status", "type": "text", "description": "Article status filter", "required": false, "order": 2}, "query": {"name": "query", "displayName": "Search Query", "type": "text", "description": "Search query", "required": false, "order": 3}}, "data": {"$ref": "#/schemas/listResponse"}, "order": 5}, "article_create": {"name": "article_create", "displayName": "Create Article", "description": "Create a new article in a knowledge base", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID", "order": 1}, "title": {"name": "title", "displayName": "Title", "type": "text", "required": true, "description": "Article title (max 255 characters)", "order": 2}, "content": {"name": "content", "displayName": "Content", "type": "text", "required": true, "description": "Article content (max 5000 characters)", "order": 3}, "status": {"name": "status", "displayName": "Status", "type": "text", "required": false, "description": "Article status (default: published)", "order": 4}, "keywords": {"name": "keywords", "displayName": "Keywords", "type": "array", "description": "Article keywords for better searchability", "required": false, "order": 5}, "customData": {"name": "customData", "displayName": "Custom Data", "type": "array", "description": "Custom data fields for additional metadata", "required": false, "order": 6}, "relatedArticles": {"name": "relatedArticles", "displayName": "Related Articles", "type": "array", "description": "Related article IDs (max 5 items)", "required": false, "order": 7}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 6}, "search": {"name": "search", "displayName": "General Search", "description": "Search across all entities", "properties": {"query": {"name": "query", "displayName": "Search Query", "type": "text", "required": true, "description": "Search query", "order": 1}, "entities": {"name": "entities", "displayName": "Entities", "type": "array", "description": "Entity types to search", "required": false, "order": 2}}, "data": {"$ref": "#/schemas/searchResponse"}, "order": 7}, "folder_retrieve": {"name": "folder_retrieve", "displayName": "Retrieve Folder", "description": "Retrieve a specific folder by ID", "properties": {"folderId": {"name": "folderId", "displayName": "Folder ID", "type": "text", "required": true, "description": "Folder ID to retrieve", "order": 1}}, "data": {"$ref": "#/schemas/folderResponse"}, "order": 8}, "folder_update": {"name": "folder_update", "displayName": "Update Folder", "description": "Update an existing folder", "properties": {"folderId": {"name": "folderId", "displayName": "Folder ID", "type": "text", "required": true, "description": "Folder ID to update", "order": 1}, "name": {"name": "name", "displayName": "Name", "type": "text", "required": true, "description": "New folder name", "order": 2}}, "data": {"$ref": "#/schemas/folderResponse"}, "order": 9}, "folder_delete": {"name": "folder_delete", "displayName": "Delete Folder", "description": "Delete a folder by ID", "properties": {"folderId": {"name": "folderId", "displayName": "Folder ID", "type": "text", "required": true, "description": "Folder ID to delete", "order": 1}}, "data": {"$ref": "#/schemas/deleteResponse"}, "order": 10}, "kb_retrieve": {"name": "kb_retrieve", "displayName": "Retrieve Knowledge Base", "description": "Retrieve a specific knowledge base by ID", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to retrieve", "order": 1}}, "data": {"$ref": "#/schemas/kbResponse"}, "order": 11}, "kb_update": {"name": "kb_update", "displayName": "Update Knowledge Base", "description": "Update an existing knowledge base", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to update", "order": 1}, "name": {"name": "name", "displayName": "Name", "type": "text", "required": true, "description": "New knowledge base name", "order": 2}, "description": {"name": "description", "displayName": "Description", "type": "text", "required": false, "description": "New knowledge base description", "order": 3}, "accessLevel": {"name": "accessLevel", "displayName": "Access Level", "type": "options", "required": true, "options": [{"name": "Private", "value": "private"}, {"name": "Public", "value": "public"}], "description": "Access level", "order": 4}}, "data": {"$ref": "#/schemas/kbResponse"}, "order": 12}, "kb_delete": {"name": "kb_delete", "displayName": "Delete Knowledge Base", "description": "Delete a knowledge base by ID", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to delete", "order": 1}}, "data": {"$ref": "#/schemas/deleteResponse"}, "order": 13}, "article_retrieve": {"name": "article_retrieve", "displayName": "Retrieve Article", "description": "Retrieve a specific article by ID", "properties": {"articleId": {"name": "articleId", "displayName": "Article ID", "type": "text", "required": true, "description": "Article ID to retrieve", "order": 1}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 14}, "article_update": {"name": "article_update", "displayName": "Update Article", "description": "Update an existing article", "properties": {"articleId": {"name": "articleId", "displayName": "Article ID", "type": "text", "required": true, "description": "Article ID to update", "order": 1}, "title": {"name": "title", "displayName": "Title", "type": "text", "required": true, "description": "New article title (max 255 characters)", "order": 2}, "content": {"name": "content", "displayName": "Content", "type": "text", "required": true, "description": "New article content (max 5000 characters)", "order": 3}, "status": {"name": "status", "displayName": "Status", "type": "text", "required": false, "description": "Article status (default: published)", "order": 4}, "keywords": {"name": "keywords", "displayName": "Keywords", "type": "array", "description": "Article keywords for better searchability", "required": false, "order": 5}, "customData": {"name": "customData", "displayName": "Custom Data", "type": "array", "description": "Custom data fields for additional metadata", "required": false, "order": 6}, "relatedArticles": {"name": "relatedArticles", "displayName": "Related Articles", "type": "array", "description": "Related article IDs", "required": false, "order": 7}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 15}, "article_delete": {"name": "article_delete", "displayName": "Delete Article", "description": "Delete an article by ID", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID containing the article", "order": 1}, "articleId": {"name": "articleId", "displayName": "Article ID", "type": "text", "required": true, "description": "Article ID to delete", "order": 2}}, "data": {"$ref": "#/schemas/deleteResponse"}, "order": 16}, "search_articles": {"name": "search_articles", "displayName": "Search Articles", "description": "Search specifically for articles", "properties": {"query": {"name": "query", "displayName": "Search Query", "type": "text", "required": true, "description": "Search query for articles", "order": 1}, "baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": false, "description": "Limit search to specific knowledge base", "order": 2}}, "data": {"$ref": "#/schemas/searchResponse"}, "order": 17}, "document_list": {"name": "document_list", "displayName": "List Documents", "description": "Retrieve a list of documents", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": false, "description": "Knowledge base ID to list documents from", "order": 1}}, "data": {"$ref": "#/schemas/listResponse"}, "order": 23}, "document_retrieve": {"name": "document_retrieve", "displayName": "Retrieve Document", "description": "Retrieve a specific document by ID", "properties": {"documentId": {"name": "documentId", "displayName": "Document ID", "type": "text", "required": true, "description": "Document ID to retrieve", "order": 1}}, "data": {"$ref": "#/schemas/documentResponse"}, "order": 24}, "document_upload": {"name": "document_upload", "displayName": "Upload Document", "description": "Upload a new document", "properties": {"file": {"name": "file", "displayName": "File", "type": "file", "required": true, "description": "File to upload", "order": 1}, "baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to upload to", "order": 2}}, "data": {"$ref": "#/schemas/documentResponse"}, "order": 25}, "document_update": {"name": "document_update", "displayName": "Update Document", "description": "Update an existing document", "properties": {"documentId": {"name": "documentId", "displayName": "Document ID", "type": "text", "required": true, "description": "Document ID to update", "order": 1}, "name": {"name": "name", "displayName": "Name", "type": "text", "required": false, "description": "New document name (max 255 chars)", "order": 2}, "accessLevel": {"name": "accessLevel", "displayName": "Access Level", "type": "options", "required": false, "options": [{"name": "Private", "value": "private"}, {"name": "Public", "value": "public"}], "description": "Document access level (default: private)", "order": 3}, "status": {"name": "status", "displayName": "Status", "type": "text", "required": false, "description": "Document upload status", "order": 4}}, "data": {"$ref": "#/schemas/documentResponse"}, "order": 26}, "document_delete": {"name": "document_delete", "displayName": "Delete Document", "description": "Delete a document by ID", "properties": {"documentId": {"name": "documentId", "displayName": "Document ID", "type": "text", "required": true, "description": "Document ID to delete", "order": 1}}, "data": {"$ref": "#/schemas/deleteResponse"}, "order": 27}, "document_download_link": {"name": "document_download_link", "displayName": "Get Document Download Link", "description": "Get a download link for a document", "properties": {"documentId": {"name": "documentId", "displayName": "Document ID", "type": "text", "required": true, "description": "Document ID to get download link for", "order": 1}}, "data": {"$ref": "#/schemas/documentDownloadLinkResponse"}, "order": 28}, "article_shortcut_create": {"name": "article_shortcut_create", "displayName": "Create Article Shortcut", "description": "Create a shortcut to an existing article", "properties": {"articleId": {"name": "articleId", "displayName": "Article ID", "type": "text", "required": true, "description": "Article ID to create shortcut for", "order": 1}, "kbId": {"name": "kbId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to create shortcut in", "order": 2}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 29}, "ask": {"name": "ask", "displayName": "Ask", "description": "Ask a question to the knowledge base", "inputs": {"properties": {"baseIds": {"name": "baseIds", "displayName": "Base IDs", "type": "array", "required": false, "description": "List of Base IDs", "order": 1}, "folderIds": {"name": "folderIds", "displayName": "Folder IDs", "type": "array", "required": false, "description": "List of Folder IDs", "order": 2}, "documentIds": {"name": "documentIds", "displayName": "Document IDs", "type": "array", "required": false, "description": "List of Document IDs", "order": 3}, "question": {"name": "question", "displayName": "Question", "type": "string", "required": true, "description": "Question to ask", "order": 4}, "mode": {"name": "mode", "displayName": "Mode", "type": "string", "required": false, "description": "Mode to use for asking", "default": "BALANCED", "order": 5}, "metadata": {"name": "metadata", "displayName": "<PERSON><PERSON><PERSON>", "type": "object", "required": false, "description": "Request metadata", "order": 6}, "stream": {"name": "stream", "displayName": "Stream", "type": "boolean", "required": false, "description": "Whether to stream the response", "order": 7}, "scoreThreshold": {"name": "scoreThreshold", "displayName": "Score Threshold", "type": "number", "required": false, "description": "Score threshold", "order": 8}, "similarityThreshold": {"name": "similarityT<PERSON><PERSON>old", "displayName": "Similarity <PERSON><PERSON><PERSON><PERSON>", "type": "number", "required": false, "description": "Similarity threshold", "order": 9}, "debug": {"name": "debug", "displayName": "Debug", "type": "boolean", "required": false, "description": "Enable debug mode", "order": 10}, "customPrompt": {"name": "customPrompt", "displayName": "Custom Prompt", "type": "string", "required": false, "description": "Custom prompt for the request", "order": 11}, "maxReferences": {"name": "maxReferences", "displayName": "Max References", "type": "number", "required": false, "description": "Maximum number of references to return", "order": 12}}}, "outputs": [{"schema": "askResponse"}]}, "custom_ask": {"name": "custom_ask", "displayName": "Custom Ask", "description": "Ask a question to the knowledge base with custom parameters", "inputs": {"properties": {"baseIds": {"name": "baseIds", "displayName": "Base IDs", "type": "array", "required": false, "description": "List of Base IDs", "order": 1}, "folderIds": {"name": "folderIds", "displayName": "Folder IDs", "type": "array", "required": false, "description": "List of Folder IDs", "order": 2}, "documentIds": {"name": "documentIds", "displayName": "Document IDs", "type": "array", "required": false, "description": "List of Document IDs", "order": 3}, "question": {"name": "question", "displayName": "Question", "type": "string", "required": true, "description": "Question to ask", "order": 4}, "mode": {"name": "mode", "displayName": "Mode", "type": "string", "required": false, "description": "Mode to use for asking", "default": "BALANCED", "order": 5}, "metadata": {"name": "metadata", "displayName": "<PERSON><PERSON><PERSON>", "type": "object", "required": false, "description": "Request metadata", "order": 6}, "stream": {"name": "stream", "displayName": "Stream", "type": "boolean", "required": false, "description": "Whether to stream the response", "order": 7}, "scoreThreshold": {"name": "scoreThreshold", "displayName": "Score Threshold", "type": "number", "required": false, "description": "Score threshold", "order": 8}, "similarityThreshold": {"name": "similarityT<PERSON><PERSON>old", "displayName": "Similarity <PERSON><PERSON><PERSON><PERSON>", "type": "number", "required": false, "description": "Similarity threshold", "order": 9}, "debug": {"name": "debug", "displayName": "Debug", "type": "boolean", "required": false, "description": "Enable debug mode", "order": 10}, "customPrompt": {"name": "customPrompt", "displayName": "Custom Prompt", "type": "string", "required": false, "description": "Custom prompt for the request", "order": 11}, "maxReferences": {"name": "maxReferences", "displayName": "Max References", "type": "number", "required": false, "description": "Maximum number of references to return", "order": 12}, "allowSearchPrivateBases": {"name": "allowSearchPrivateBases", "displayName": "Allow Search Private Bases", "type": "boolean", "required": false, "description": "Allow searching private knowledge bases", "order": 13}, "entities": {"name": "entities", "displayName": "Entities", "type": "array", "required": false, "description": "List of entity types to search", "order": 14}}}, "outputs": [{"schema": "customAskResponse"}]}, "agent": {"name": "agent", "displayName": "Agent", "description": "Use an agent to interact with the knowledge base", "inputs": {"properties": {"baseIds": {"name": "baseIds", "displayName": "Base IDs", "type": "array", "required": false, "description": "List of Base IDs", "order": 1}, "folderIds": {"name": "folderIds", "displayName": "Folder IDs", "type": "array", "required": false, "description": "List of Folder IDs", "order": 2}, "documentIds": {"name": "documentIds", "displayName": "Document IDs", "type": "array", "required": false, "description": "List of Document IDs", "order": 3}, "question": {"name": "question", "displayName": "Question", "type": "string", "required": true, "description": "Question to ask", "order": 4}, "agent": {"name": "agent", "displayName": "Agent", "type": "string", "required": true, "description": "Agent to use for interaction", "order": 5}, "mode": {"name": "mode", "displayName": "Mode", "type": "string", "required": false, "description": "Mode to use for asking", "default": "BALANCED", "order": 6}, "metadata": {"name": "metadata", "displayName": "<PERSON><PERSON><PERSON>", "type": "object", "required": false, "description": "Request metadata", "order": 7}, "stream": {"name": "stream", "displayName": "Stream", "type": "boolean", "required": false, "description": "Whether to stream the response", "order": 8}, "scoreThreshold": {"name": "scoreThreshold", "displayName": "Score Threshold", "type": "number", "required": false, "description": "Score threshold", "order": 9}, "similarityThreshold": {"name": "similarityT<PERSON><PERSON>old", "displayName": "Similarity <PERSON><PERSON><PERSON><PERSON>", "type": "number", "required": false, "description": "Similarity threshold", "order": 10}, "debug": {"name": "debug", "displayName": "Debug", "type": "boolean", "required": false, "description": "Enable debug mode", "order": 11}, "customPrompt": {"name": "customPrompt", "displayName": "Custom Prompt", "type": "string", "required": false, "description": "Custom prompt for the request", "order": 12}, "maxReferences": {"name": "maxReferences", "displayName": "Max References", "type": "number", "required": false, "description": "Maximum number of references to return", "order": 13}, "allowSearchPrivateBases": {"name": "allowSearchPrivateBases", "displayName": "Allow Search Private Bases", "type": "boolean", "required": false, "description": "Allow searching private knowledge bases", "order": 14}, "entities": {"name": "entities", "displayName": "Entities", "type": "array", "required": false, "description": "List of entity types to search", "order": 15}}}, "outputs": [{"schema": "agentResponse"}]}}}