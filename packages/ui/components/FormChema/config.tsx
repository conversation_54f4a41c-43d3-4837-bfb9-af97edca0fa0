import { ComponentConfig } from './type';
import { SchemaTextInput } from './components/TextInput';
import { SchemaPasswordInput } from './components/PasswordInput';
import { SchemaCheckbox } from './components/Checkbox';
import { SchemaRadio } from './components/Radio';
import { SchemaSelectOption, SchemaSelect } from './components/SelectOption';
import { SchemaDisplayName } from './components/DisplayName';
import { SchemaCode } from './components/Code';
import { SchemaTextareaInput } from './components/TextareaInput';
import { SchemaComboboxSelectDataPoint } from './components/ComboboxSelectDataPoint';
import { SchemaURLInput } from './components/URLInput';

export const SCHEMA_CONFIGS: Record<string, ComponentConfig<any>> = {
  textInput: {
    id: 'textInput',
    types: ['text', 'number', 'object', 'datetime', 'string', 'integer'],
    Component: SchemaTextInput,
  },
  passwordInput: {
    id: 'passwordInput',
    types: ['password', 'text-masked'],
    Component: SchemaPasswordInput,
  },
  checkbox: {
    id: 'checkbox',
    types: ['boolean', 'checkbox'],
    Component: SchemaCheckbox,
  },
  displayName: {
    id: 'displayName',
    types: ['displayName'],
    Component: SchemaDisplayName,
  },
  selectOption: {
    id: 'selectOption',
    types: ['options'],
    Component: SchemaSelectOption,
  },
  select: {
    id: 'select',
    types: ['select'],
    Component: SchemaSelect,
  },
  radio: {
    id: 'radio',
    types: ['radio'],
    Component: SchemaRadio,
  },
  textarea: {
    id: 'textarea',
    types: ['textarea'],
    Component: SchemaTextareaInput,
  },
  code: {
    id: 'code',
    types: ['code'],
    Component: SchemaCode,
  },
  comboboxSelectDataPoint: {
    id: 'comboboxSelectDataPoint',
    types: ['comboboxDataPoint'],
    Component: SchemaComboboxSelectDataPoint,
  },
  urlInput: {
    id: 'urlInput',
    types: ['url'],
    Component: SchemaURLInput,
  },
};
