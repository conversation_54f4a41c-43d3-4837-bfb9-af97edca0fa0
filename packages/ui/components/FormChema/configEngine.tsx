import { ComponentConfig } from './type';
import { FormTextInput } from './components/TextInput';
import { FormPasswordInput } from './components/PasswordInput';
import { FormCheckbox } from './components/Checkbox';
import { FormRadio } from './components/Radio';
import { FormSelect, FormSelectOption } from './components/SelectOption';
import { SchemaDisplayName } from './components/DisplayName';
import { FormPathCondition } from './components/PathCondition';
import { FormKeyValueInput } from './components/KeyValueInput';
import { FormCode } from './components/Code';
import { FormTextareaInput } from './components/TextareaInput';
import { FormMultipleSelectOption } from './components/MultipleSelectOption';
import { FormArrayTextInput } from './components/ArrayTextInput';
import { FormArrayObject } from './components/ArrayObject';
import { FormComboboxSelectDataPoint } from './components/ComboboxSelectDataPoint';
import { FormGroup } from './components/Group';
import { FormURLInput } from './components/URLInput';

// SchemaEngine config
export const SCHEMA_CONFIGS_ENGINE: Record<string, ComponentConfig<any>> = {
  textInput: {
    id: 'textInput',
    types: ['text', 'number', 'object', 'datetime', 'string', 'integer'],
    Component: FormTextInput,
  },
  passwordInput: {
    id: 'passwordInput',
    types: ['password', 'text-masked'],
    Component: FormPasswordInput,
  },
  checkbox: {
    id: 'checkbox',
    types: ['boolean', 'checkbox'],
    Component: FormCheckbox,
  },
  displayName: {
    id: 'displayName',
    types: ['displayName'],
    Component: SchemaDisplayName,
  },
  selectOption: {
    id: 'selectOption',
    types: ['options'],
    Component: FormSelectOption,
  },
  multipleSelectOption: {
    id: 'multipleSelectOption',
    types: ['multiOptions'],
    Component: FormMultipleSelectOption,
  },
  select: {
    id: 'select',
    types: ['select'],
    Component: FormSelect,
  },
  radio: {
    id: 'radio',
    types: ['radio'],
    Component: FormRadio,
  },
  keyValueInput: {
    id: 'keyValueInput',
    types: ['keyvalue'],
    Component: FormKeyValueInput,
  },
  arrayInput: {
    id: 'arrayObject',
    types: ['arrayObject'],
    Component: FormArrayObject,
  },
  pathCondition: {
    id: 'condition',
    types: ['condition'],
    Component: FormPathCondition,
  },
  textarea: {
    id: 'textarea',
    types: ['textarea'],
    Component: FormTextareaInput,
  },
  code: {
    id: 'code',
    types: ['code'],
    Component: FormCode,
  },
  textArray: {
    id: 'textArray',
    types: ['array', 'object'],
    Component: FormArrayTextInput,
  },
  comboboxSelectDataPoint: {
    id: 'comboboxSelectDataPoint',
    types: ['comboboxDataPoint'],
    Component: FormComboboxSelectDataPoint,
  },
  group: {
    id: 'group',
    types: ['group'],
    Component: FormGroup,
  },
  urlInput: {
    id: 'urlInput',
    types: ['url'],
    Component: FormURLInput,
  },
};
