import React, { useEffect, useState } from 'react';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import { Box, Text, Textarea } from '@mantine/core';
import { customStyles } from '../../../constants/customStyleMonacoEditor';

export interface SchemaCodeProps {
  value?: string;
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  withContainer?: boolean;
  error?: string;
}

// Type for Monaco Editor component
interface MonacoEditorProps {
  height: string;
  defaultLanguage: string;
  value: string;
  onChange: (value: string | undefined) => void;
  options: Record<string, any>;
}

export const SchemaCode: React.FC<SchemaCodeProps> = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaCodeProps) => {
  const [value, setValue] = useState(valueProp || schema.default || '');
  const [MonacoEditor, setMonacoEditor] = useState<React.ComponentType<MonacoEditorProps> | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setValue(valueProp || schema.default || '');
  }, [valueProp, schema.default]);

  // Try to dynamically import Monaco
  useEffect(() => {
    setIsLoading(true);
    const importMonaco = async () => {
      try {
        // Using dynamic import to avoid direct dependency
        // This will only work if the package is available in the app
        // @ts-ignore - Dynamic import that might not exist
        const monaco = await import('@monaco-editor/react').catch(() => null);

        if (monaco && monaco.default) {
          setMonacoEditor(() => monaco.default);
        }
      } catch (error) {
        // Silently fail and use textarea instead
        console.warn('Monaco editor not available, using textarea instead');
      } finally {
        setIsLoading(false);
      }
    };

    importMonaco();
  }, []);

  const handleEditorChange = (value: string | undefined) => {
    if (value === undefined) return;
    setValue(value);
    onChange?.(value);
  };

  // Determine the language based on schema or default to javascript
  const language = schema.language || 'javascript';

  return (
    <Box>
      {schema.displayName && (
        <Text fw={500} size='sm' mb={3}>
          {schema.displayName}
          {schema.required && <span style={{ color: 'red' }}> *</span>}
        </Text>
      )}

      {schema.description && (
        <Text c='dimmed' size='xs' mb={5}>
          {schema.description}
        </Text>
      )}

      {isLoading ? (
        // Show a simple loading state while we try to load Monaco
        <div
          style={{
            height: '200px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#888',
          }}>
          Loading editor...
        </div>
      ) : MonacoEditor ? (
        // Use Monaco editor if available
        <MonacoEditor
          height={schema.height || '200px'}
          defaultLanguage={language}
          value={value}
          onChange={handleEditorChange}
          options={customStyles}
        />
      ) : (
        // Fallback to Mantine's Textarea if Monaco is not available
        <Textarea
          value={value}
          onChange={e => {
            setValue(e.target.value);
            onChange?.(e.target.value);
          }}
          placeholder={schema.placeholder}
          error={error}
          minRows={schema.minRows || 5}
          maxRows={schema.maxRows || 20}
          styles={{
            input: {
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              overflowX: 'auto',
              minHeight: '200px',
            },
          }}
        />
      )}
    </Box>
  );
};

// For applications that want to use Monaco, they should ensure @monaco-editor/react is installed

export const SchemaCodeWithContainer = withContainer(SchemaCode);
export const FormCode = withReactHookForm(SchemaCode);
export const FormCodeWithContainer = withReactHookForm(SchemaCodeWithContainer);
