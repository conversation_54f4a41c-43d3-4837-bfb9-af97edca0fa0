import { Button, Flex, Group, Paper, Stack, Text, TextInput, rem } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { Control } from 'react-hook-form';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import { SCHEMA_CONFIGS } from '../config';

export interface SchemaArrayInputBaseProps {
  value?: string;
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  withContainer?: boolean;
  error?: string;
  control?: Control<any>;
}

const ArrayTextInputBase = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaArrayInputBaseProps) => {
  const initData = useMemo<any[]>(() => {
    const data = valueProp || schema?.default || '[]';
    const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
    return Array.isArray(parsedData) && parsedData.length ? parsedData : [''];
  }, [schema?.default, valueProp]);

  const [values, setValues] = useState(initData);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // // Check if this is an array of objects
  const isObjectArray = useMemo(() => {
    return schema?.items?.type === 'object' && schema?.items?.properties;
  }, [schema]);

  const handleOnChangeValuesToJsonString = useCallback(
    values => {
      return onChange?.(values);
    },
    [onChange]
  );

  const handleAddNewValue = () => {
    if (isObjectArray) {
      // Create a new object with default values for each property
      const newObject = Object.keys(schema.items.properties).reduce(
        (acc, key) => {
          acc[key] = schema.items.properties[key].default || '';
          return acc;
        },
        {} as Record<string, any>
      );
      const newValue = [...values, newObject];
      setValues(newValue);
      handleOnChangeValuesToJsonString(newValue);
    } else {
      const newValue = [...values, ''];
      setValues(newValue);
      handleOnChangeValuesToJsonString(newValue);
    }

    // Scroll to the bottom after adding new value
    setTimeout(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
      }
    }, 100);
  };

  const handleRemoveValue = (index: number) => {
    const newValues = [...values];
    if (newValues.length === 1) {
      if (isObjectArray) {
        newValues[0] = Object.keys(schema.items.properties).reduce(
          (acc, key) => {
            acc[key] = '';
            return acc;
          },
          {} as Record<string, any>
        );
      } else {
        newValues[0] = '';
      }
      setValues(newValues);
      handleOnChangeValuesToJsonString([]);
      return;
    }
    newValues.splice(index, 1);
    setValues(newValues);
    handleOnChangeValuesToJsonString(newValues);
  };

  const debounceChange = useMemo(() => {
    return debounce(handleOnChangeValuesToJsonString, 300);
  }, [handleOnChangeValuesToJsonString]);

  const handleValueChange = (index: number, value: string) => {
    const newValue = [...values];
    newValue[index] = value?.trim();
    setValues(newValue);
    debounceChange(newValue);
  };

  const handleObjectValueChange = (index: number, propertyKey: string, value: any) => {
    const newValue = [...values];
    if (!newValue[index]) {
      newValue[index] = {};
    }
    newValue[index][propertyKey] = value;
    setValues(newValue);
    debounceChange(newValue);
  };

  useEffect(() => {
    setValues(initData);
  }, [initData]);

  // Render object properties for each array item
  const renderObjectItem = (item: any, index: number) => {
    const properties = schema.items.properties;

    return (
      <Paper key={index} p='md' withBorder mt={rem(4)}>
        <Group justify='space-between' mb='md'>
          <Text fw={500} fz='sm'>
            {`${schema.displayName} ${index + 1}`}
          </Text>
          <Button size='xs' variant='light' onClick={() => handleRemoveValue(index)}>
            <IconX size={15} />
          </Button>
        </Group>
        <Stack gap='md'>
          {Object.entries(properties).map(([propertyKey, propertySchema]: [string, any]) => {
            const config = Object.values(SCHEMA_CONFIGS).find(
              config => Array.isArray(config.types) && config.types.includes(propertySchema.type)
            );
            const FieldComponent = config?.Component;

            if (!FieldComponent) {
              return (
                <Text c='blue' fw={500} size='lg' ta='center' key={`${index}-${propertyKey}`}>
                  Unsupported schema type: {JSON.stringify(propertySchema.type)}
                </Text>
              );
            }
            return (
              <FieldComponent
                key={`${index}-${propertyKey}`}
                schema={propertySchema}
                onChange={(val: any) => {
                  handleObjectValueChange(index, propertyKey, val);
                }}
                value={item?.[propertyKey] || ''}
                error={error}
              />
            );
          })}
        </Stack>
      </Paper>
    );
  };

  return (
    <Flex direction='column'>
      <Text fw={500} fz='sm'>
        {schema.displayName}
        {schema.required && <span style={{ color: '#fa5252' }}> *</span>}
      </Text>
      <Text fz='xs' c='dimmed'>
        {schema.description}
      </Text>
      <Flex
        ref={scrollContainerRef}
        direction='column'
        gap='md'
        style={{
          minHeight: rem(30),
          maxHeight: rem(400),
          overflowY: 'auto',
          paddingRight: rem(5),
        }}
      >
        {values.map((value, index) => {
          if (isObjectArray) {
            return renderObjectItem(value, index);
          }

          return (
            <Group key={index} justify='space-between' w='100%'>
              <TextInput
                size='xs'
                styles={{ root: { flexGrow: 1 } }}
                value={value}
                error={error}
                onChange={e => handleValueChange(index, e.target.value)}
                placeholder={schema.placeholder}
                labelProps={{
                  required: schema.required,
                }}
              />
              <Button size='xs' variant='light' onClick={() => handleRemoveValue(index)}>
                <IconX size={15} />
              </Button>
            </Group>
          );
        })}
      </Flex>
      <Group justify='right' mt='sm'>
        <Button onClick={handleAddNewValue}>
          Add New Value
        </Button>
      </Group>
    </Flex>
  );
};

export const SchemaArrayTextInput = ArrayTextInputBase;
export const SchemaArrayTextInputWithContainer = withContainer(SchemaArrayTextInput);
export const FormArrayTextInput = withReactHookForm(SchemaArrayTextInput);
export const FormTextInputWithContainer = withReactHookForm(SchemaArrayTextInputWithContainer);
