import { rem } from '@mantine/core';
import { SimpleGrid } from '@mantine/core';
import { Accordion } from '@mantine/core';
import { CategoryArticle } from '../../types/pageBuilder';
import { IconChevronRight } from '@tabler/icons-react';
import { createStyles } from '@mantine/emotion';
import { BlockNoteViewer } from '@resola-ai/blocknote-editor';
import {
  createResponsiveValue,
  generateResponsiveDimension,
  generateResponsiveGridColumns,
  generateResponsivePadding,
  generateResponsiveStyles,
  isResponsiveProp,
} from '../../utils';
import { DEFAULT_BOX_VALUES } from '../../constants/page-builder';

const useStyles = createStyles(() => ({
  root: {
    marginBottom: 2,
  },
  chevron: {
    '&[data-rotate]': {
      transform: 'rotate(90deg)',
    },
  },
  item: {
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    '.mantine-Accordion-label': {
      padding: 0,
    },

    '&[data-active]': {
      '.mantine-Accordion-control': {
        borderBottomLeftRadius: '0 !important',
        borderBottomRightRadius: '0 !important',
      },
    },
    button: {
      '&:hover': {
        backgroundColor: 'transparent',
      },
    },
  },
}));

const BoxyArticlesElement = (props: Record<string, any>) => {
  const { classes } = useStyles();
  const {
    selectedArticles = [],
    showAccordion,
    showBorder,
    borderColor,
    cornerRadius,
    columns,
    gap,
    questionBox = DEFAULT_BOX_VALUES,
    answerBox = DEFAULT_BOX_VALUES,
  } = props;

  const borderRadius = isResponsiveProp(cornerRadius)
    ? createResponsiveValue(
        `${cornerRadius.mobile}px`,
        `${cornerRadius.tablet}px`,
        `${cornerRadius.desktop}px`
      )
    : rem(cornerRadius);

  return (
    <Accordion
      w={'100%'}
      classNames={{
        root: classes.root,
        chevron: classes.chevron,
        item: classes.item,
      }}
      onChange={(value: string | null) => {
        if (value) props?.onChangeArticle(value);
      }}
      chevron={showAccordion ? <IconChevronRight /> : false}
      transitionDuration={100}
      styles={generateResponsiveStyles({
        control: {
          padding: generateResponsivePadding(questionBox.padding),
          backgroundColor: questionBox.backgroundColor,
          color: questionBox.textColor,
          borderRadius: borderRadius,
        },
        item: {
          borderRadius: borderRadius,
          border: showBorder ? `1px solid ${borderColor}` : 'none',
        },
        panel: {
          padding: generateResponsivePadding(answerBox.padding),
          backgroundColor: answerBox.backgroundColor,
          color: answerBox.textColor,
          borderBottomLeftRadius: borderRadius,
          borderBottomRightRadius: borderRadius,
          borderTop: answerBox.showDivider ? `1px solid ${borderColor}` : 'none',
          boxShadow: answerBox.showDivider ? `0px 1px ${borderColor}` : 'none',
        },
      })}>
      <SimpleGrid
        styles={generateResponsiveStyles({
          gap: generateResponsiveDimension(gap, '0px'),
          gridTemplateColumns: generateResponsiveGridColumns(columns),
        })}
        w={'100%'}>
        {selectedArticles.map((article: CategoryArticle) => (
          <Accordion.Item w={'100%'} value={article.value} key={article.value}>
            <Accordion.Control fw={500} fz={rem(16)}>
              {article.label}
            </Accordion.Control>
            <Accordion.Panel>
              <BlockNoteViewer initialHTML={article.contentRaw} textColor={answerBox.textColor} />
            </Accordion.Panel>
          </Accordion.Item>
        ))}
      </SimpleGrid>
    </Accordion>
  );
};

export default BoxyArticlesElement;
