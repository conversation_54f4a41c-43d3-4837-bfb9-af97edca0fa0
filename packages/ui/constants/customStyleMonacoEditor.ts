export const customStyles = {
  minimap: {
    enabled: false,
  },
  lineDecorationsWidth: 20,
  lineNumbers: 'on',
  wordWrap: 'on',
  wrappingStrategy: 'advanced',
  wrappingIndent: 'indent',
  lineNumbersMinChars: 3,
  glyphMargin: false,
  folding: false,
  // Remove all borders
  renderLineHighlight: 'none',
  tabSize: 2,
  indentSize: 2,
  // Style scrollbar
  overviewRulerBorder: false,
  overviewRulerLanes: 0,
  scrollBeyondLastLine: false,
  scrollbar: {
    vertical: 'visible',
    horizontal: 'visible',
    verticalScrollbarSize: 8,
    horizontalScrollbarSize: 8,
    useShadows: false,
    verticalHasArrows: false,
    horizontalHasArrows: false,
    arrowSize: 0,
    borderRadius: '4px',
  },
  padding: {
    top: 20,
    bottom: 10,
    left: 10,
    right: 10,
  },
  quickSuggestions: {
    other: false,
    comments: false,
    strings: false
  },
  parameterHints: {
    enabled: false
  },
  suggestOnTriggerCharacters: false,
  acceptSuggestionOnEnter: "off",
  tabCompletion: "off",
  wordBasedSuggestions: false,
} as const;
