import React, { memo, useCallback, useEffect, useMemo } from 'react';
import { Box } from '@mantine/core';
import isNil from 'lodash/isNil';
import BlockNoteSchema from './BlockNoteSchema';
import BlockNoteLocales from './BlockNoteLocales';
import BlockNoteThemeProvider from './BlockNoteThemeProvider';
import { useBlockNoteStyles, useFilterSuggestions } from '../hooks';
import { useBlockNoteEditor } from '../hooks/useBlockNoteEditor';
import {
  loadEditorContent,
  processBlocksForMarkdown,
  serializeToMarkdown,
} from '../utils/editorContent';
import { MarkdownEditorProps } from '../types/blocknote';
import BlockNoteEditorRenderer from './BlockNoteEditorRenderer';

import '@blocknote/core/fonts/inter.css';
import '@blocknote/shadcn/style.css';

/**
 * BlockNoteMarkdown - A markdown editor component built on top of BlockNote
 *
 * @important CSS Import Required: You must import the CSS file in your main application file:
 * ```tsx
 * import '@resola-ai/blocknote-editor/styles.css';
 * ```
 *
 * @param props - MarkdownEditorProps containing editor configuration
 * @returns React component for the BlockNote markdown editor
 */
const BlockNoteMarkdown: React.FC<MarkdownEditorProps> = props => {
  const {
    className,
    initialMarkdown = '',
    isBordered = true,
    isEditable = true,
    usingCustomFormattingToolbar = true,
    usingCustomLinkToolbar = true,
    usingCustomFilePanel = false,
    usingCustomSuggestionVariable = false,
    variableSuggestions = [],
    language = 'en',
    onChange,
    onBlur,
    onFocus,
    autoFocus,
  } = props;

  const dictionary = useMemo(() => BlockNoteLocales[language], [language]);

  const { filterVariableSuggestions } = useFilterSuggestions({
    variableSuggestions,
  });

  // Use the shared editor hook
  const { editor, editorRef, handleAutoFocus } = useBlockNoteEditor({
    schema: BlockNoteSchema,
    language,
    autoFocus,
    onBlur,
    onFocus,
  });

  const { classes, cx } = useBlockNoteStyles({ isBordered });

  /**
   * Load initial Markdown content to the editor
   * @param markdownContent Markdown content to load
   * @returns void
   */
  const loadInitialMarkdown = useCallback(
    async (markdownContent: string) => {
      // Use shared content loading utility
      const blocks = await loadEditorContent(editor, markdownContent, true);

      // Process blocks for markdown
      const parsedBlocks = processBlocksForMarkdown(blocks);

      editor.replaceBlocks(editor.document, parsedBlocks);

      if (autoFocus) {
        handleAutoFocus();
      }
    },
    [editor, autoFocus, handleAutoFocus]
  );

  /**
   * Handle editor change event
   * @returns void
   */
  const handleEditorChange = useCallback(async () => {
    // Use shared serialization utility
    const finalMarkdown = await serializeToMarkdown(editor);

    onChange?.(finalMarkdown);
  }, [editor, onChange]);

  /**
   * Load initial Markdown content on mount
   * @returns void
   */
  useEffect(() => {
    if (!isNil(initialMarkdown)) {
      loadInitialMarkdown(initialMarkdown);
    }
  }, [initialMarkdown, loadInitialMarkdown]);

  // Data attributes for the editor
  const dataAttributes = {
    'data-blocknote-editor': '',
  };

  return (
    <BlockNoteThemeProvider>
      <Box>
        <BlockNoteEditorRenderer
          ref={editorRef}
          editor={editor}
          className={cx(classes.editorContainer, classes.markdownEditor, className)}
          editable={isEditable}
          onChange={handleEditorChange}
          theme='light'
          formattingToolbarEnabled={usingCustomFormattingToolbar}
          linkToolbarEnabled={usingCustomLinkToolbar}
          filePanelEnabled={usingCustomFilePanel}
          sideMenuEnabled={true}
          suggestionVariableEnabled={usingCustomSuggestionVariable}
          variableSuggestionHandler={async query => filterVariableSuggestions(editor, query)}
          dictionary={dictionary}
          dataAttributes={dataAttributes}
          enabledNestedBlock={false}
          enabledTextAlignment={false}
        />
      </Box>
    </BlockNoteThemeProvider>
  );
};

export default memo(BlockNoteMarkdown);
