import React, { memo, forwardRef } from 'react';
import { BlockNoteEditor } from '@blocknote/core';
import { BlockNoteView as BlockNoteViewComponent } from '@blocknote/shadcn';
import {
  FilePanelController,
  LinkToolbarController,
  SideMenuController,
  SuggestionMenuController,
  FilePanelProps,
} from '@blocknote/react';
import type { InlineContentSchema, StyleSchema } from '@blocknote/core';
import type { LinkToolbarProps as BlockNoteLinkToolbarProps } from '../CustomComponents/LinkToolbar/LinkToolbarProps';
import { FilePanel, LinkToolbar } from '../CustomComponents';
import BlockNoteToolbar from '../BlockNoteToolbar';
import { SideMenuWrapper } from '../SideMenuWrapper';
import { renderWhenEnabled, type ControlComponentType } from '../../utils/componentWrappers';

export type BlockNoteEditorRendererProps = {
  editor: BlockNoteEditor;
  className?: string;
  editable?: boolean;
  onChange?: () => void;
  theme?: 'light' | 'dark';
  pasteHandler?: (event: React.ClipboardEvent<HTMLDivElement>) => Promise<boolean>;
  formattingToolbarEnabled?: boolean;
  linkToolbarEnabled?: boolean;
  filePanelEnabled?: boolean;
  sideMenuEnabled?: boolean;
  suggestionVariableEnabled?: boolean;
  variableTriggerChar?: string;
  variableSuggestionHandler?: (query: string) => Promise<any[]>;
  dictionary?: Record<string, any>;
  customLinkToolbar?: ControlComponentType;
  customFilePanel?: ControlComponentType;
  customSideMenu?: ControlComponentType;
  enabledNestedBlock?: boolean;
  enabledTextAlignment?: boolean;
  dataAttributes?: Record<string, string>;
  ariaAttributes?: Record<string, string>;
};

// Updated prop definitions to avoid intersections with `any` and improve type safety

interface FilePanelWrapperProps<
  TSchema extends InlineContentSchema = InlineContentSchema,
  TEditor extends StyleSchema = StyleSchema
> extends FilePanelProps<TSchema, TEditor> {
  customComponent?: ControlComponentType;
}

interface LinkToolbarWrapperProps extends BlockNoteLinkToolbarProps {
  customComponent?: ControlComponentType;
}

// Memoized LinkToolbar wrapper with proper display name
const LinkToolbarWrapper = React.memo(function LinkToolbarWrapper({
  customComponent,
  ...props
}: LinkToolbarWrapperProps) {
  return customComponent ? React.createElement(customComponent, props) : <LinkToolbar {...props} />;
});

// Memoized FilePanel wrapper with proper display name
const FilePanelWrapper = React.memo(function FilePanelWrapper({
  customComponent,
  ...props
}: FilePanelWrapperProps) {
  return customComponent ? React.createElement(customComponent, props) : <FilePanel {...props} />;
});

// Create a wrapped BlockNoteView that isolates ref-related issues (same as in BlockNoteViewer)
const BlockNoteViewWrapper = memo((props: any) => {
  // This component acts as a buffer to prevent ref warnings from bubbling up
  return <BlockNoteViewComponent {...props} />;
});

BlockNoteViewWrapper.displayName = 'BlockNoteViewWrapper';

/**
 * Shared component for rendering BlockNote editor content with various controls
 */
const BlockNoteEditorRendererComponent = forwardRef<HTMLDivElement, BlockNoteEditorRendererProps>(
  (
    {
      editor,
      className,
      editable = true,
      onChange,
      theme = 'light',
      formattingToolbarEnabled = true,
      linkToolbarEnabled = true,
      filePanelEnabled = true,
      sideMenuEnabled = true,
      suggestionVariableEnabled = false,
      variableTriggerChar = '{',
      variableSuggestionHandler,
      dictionary,
      customLinkToolbar,
      customFilePanel,
      customSideMenu,
      enabledNestedBlock = true,
      enabledTextAlignment = true,
      dataAttributes = {},
      ariaAttributes = {},
    },
    forwardedRef
  ) => {
    // Create the file panel component that will be used by the controller
    const FilePanelComponent = React.useCallback(
      (props: FilePanelProps) => (
        <FilePanelWrapper {...props} customComponent={customFilePanel} />
      ),
      [customFilePanel]
    );

    // Create the link toolbar component that will be used by the controller
    const LinkToolbarComponent = React.useCallback(
      (props: BlockNoteLinkToolbarProps) => (
        <LinkToolbarWrapper {...props} customComponent={customLinkToolbar} />
      ),
      [customLinkToolbar]
    );

    // Create the side menu component that will be used by the controller
    const SideMenuComponent = React.useCallback(
      (props: any) => (
        <SideMenuWrapper
          {...props}
          customComponent={customSideMenu}
          dictionary={dictionary}
        />
      ),
      [customSideMenu, dictionary]
    );

    // Create props for base component with inverted boolean logic helper
    const createNegatedProps = (flags: Record<string, boolean>) => {
      return Object.entries(flags).reduce(
        (acc, [key, value]) => {
          acc[key] = !value;
          return acc;
        },
        {} as Record<string, boolean>
      );
    };

    const baseProps = {
      editor,
      className,
      editable,
      onChange,
      theme,
      ...createNegatedProps({
        formattingToolbar: formattingToolbarEnabled,
        linkToolbar: linkToolbarEnabled,
        filePanel: filePanelEnabled,
        sideMenu: sideMenuEnabled,
      }),
      ...dataAttributes,
      ...ariaAttributes,
    };

    // Create a custom suggestion handler
    const enhancedSuggestionHandler = async (query: string) => {
      // If suggestions are disabled or no handler provided, return empty array
      if (!suggestionVariableEnabled || !variableSuggestionHandler) {
        return [];
      }

      try {
        const items = await variableSuggestionHandler(query);
        return items ?? [];
      } catch (error) {
        console.error('Error in variable suggestion handler:', error);
        return [];
      }
    };

    return (
      <div ref={forwardedRef}>
        <BlockNoteViewWrapper {...baseProps}>
          {/* File Panel */}
          {renderWhenEnabled(filePanelEnabled, () => (
            <div data-editor-control>
              <FilePanelController filePanel={FilePanelComponent} />
            </div>
          ))}

          {/* Link Toolbar */}
          {renderWhenEnabled(linkToolbarEnabled, () => (
            <div data-editor-control>
              <LinkToolbarController linkToolbar={LinkToolbarComponent} />
            </div>
          ))}

          {/* Formatting Toolbar */}
          {renderWhenEnabled(formattingToolbarEnabled, () => (
            <div data-editor-control data-editor-control-formatting-toolbar>
              <BlockNoteToolbar
                enabledNestedBlock={enabledNestedBlock}
                enabledTextAlignment={enabledTextAlignment}
              />
            </div>
          ))}

          {/* Side Menu */}
          {renderWhenEnabled(sideMenuEnabled, () => (
            <div data-editor-control>
              <SideMenuController sideMenu={SideMenuComponent} />
            </div>
          ))}

          {/* Variable Suggestions */}
          {suggestionVariableEnabled && (
            <div data-editor-control>
              <SuggestionMenuController
                triggerCharacter={variableTriggerChar}
                getItems={enhancedSuggestionHandler}
              />
            </div>
          )}
        </BlockNoteViewWrapper>
      </div>
    );
  }
);

// Add display name for better debugging
BlockNoteEditorRendererComponent.displayName = 'BlockNoteEditorRenderer';

// Main export - keeping the established name for consistency with existing usage
export const BlockNoteEditorRenderer = BlockNoteEditorRendererComponent;

// Default export for standard import patterns
export default BlockNoteEditorRenderer;
