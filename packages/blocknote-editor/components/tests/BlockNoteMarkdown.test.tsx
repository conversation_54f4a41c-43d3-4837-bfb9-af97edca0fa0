import React, { forwardRef } from 'react';
import { render, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createEmotionCache } from '@mantine/core';
import BlockNoteMarkdown from '../BlockNoteMarkdown';

// Mock all external modules
vi.mock('@mantine/core', () => ({
  createEmotionCache: vi.fn(() => ({ key: 'mantine-test' })),
  MantineProvider: ({ children }) => <div data-testid='mantine-provider'>{children}</div>,
  Box: forwardRef(({ children, ...props }, ref) => (
    <div data-testid='mantine-box' ref={ref} {...props}>
      {children}
    </div>
  )),
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      editorContainer: 'mock-editor-container',
      markdownEditor: 'mock-markdown-editor',
    },
    cx: (...classNames) => classNames.filter(Boolean).join(' '),
  }),
  MantineEmotionProvider: ({ children, value }) => (
    <div data-testid='mantine-emotion-provider'>{children}</div>
  ),
  useEmotionCache: vi.fn(() => ({})),
  emotionTransform: { type: 'selector-transform' },
}));

// Mock editor instance
const mockEditor = {
  _tiptapEditor: {
    on: vi.fn(),
    off: vi.fn(),
  },
  document: [],
  replaceBlocks: vi.fn(),
  tryParseMarkdownToBlocks: vi.fn().mockResolvedValue([]),
  blocksToMarkdownLossy: vi.fn().mockResolvedValue(''),
  focus: vi.fn(),
  dictionary: undefined,
};

vi.mock('@blocknote/react', () => ({
  useCreateBlockNote: vi.fn(props => {
    // Update the dictionary property based on props
    if (props && props.dictionary) {
      mockEditor.dictionary = props.dictionary;
    }
    return mockEditor;
  }),
  createReactBlockSpec: vi.fn(() => ({})),
  FilePanelController: ({ filePanel }) => (
    <div data-testid='file-panel-controller'>{filePanel && React.createElement(filePanel)}</div>
  ),
  LinkToolbarController: ({ linkToolbar }) => (
    <div data-testid='link-toolbar-controller'>
      {linkToolbar &&
        React.createElement(linkToolbar, { title: 'test', url: 'https://example.com' })}
    </div>
  ),
  SideMenuController: ({ sideMenu }) => (
    <div data-testid='side-menu-controller'>{sideMenu && React.createElement(sideMenu, {})}</div>
  ),
  RemoveBlockItem: ({ children }) => <div data-testid='remove-block-item'>{children}</div>,
  DragHandleMenu: ({ children }) => <div data-testid='drag-handle-menu'>{children}</div>,
  SuggestionMenuController: ({ triggerCharacter, getItems }) => (
    <div
      data-testid='suggestion-menu-controller'
      data-trigger-character={triggerCharacter}
      onClick={() => getItems && getItems('test')}>
      Suggestion Menu
    </div>
  ),
}));

vi.mock('@blocknote/shadcn', () => ({
  BlockNoteView: props => {
    const {
      formattingToolbar,
      linkToolbar,
      filePanel,
      sideMenu,
      editable,
      children,
      ...restProps
    } = props;
    return (
      <div
        data-testid='blocknote-view'
        className={props.className}
        data-theme={props.theme}
        data-formatting-toolbar={formattingToolbar ? 'true' : 'false'}
        data-link-toolbar={linkToolbar ? 'true' : 'false'}
        data-file-panel={filePanel ? 'true' : 'false'}
        data-side-menu={sideMenu ? 'true' : 'false'}
        data-editable={editable ? 'true' : 'false'}
        {...restProps}>
        {children}
      </div>
    );
  },
}));

// Mock all internal modules
vi.mock('../BlockNoteSchema', () => ({
  default: {
    // Mock schema object
    blockSpecs: {},
    inlineContentSpecs: {},
    styleSpecs: {},
  },
}));

vi.mock('../BlockNoteLocales', () => ({
  default: {
    en: { drag_handle: { delete_menuitem: 'Delete' } },
    fr: { drag_handle: { delete_menuitem: 'Supprimer' } },
  },
}));

vi.mock('../BlockNoteToolbar', () => ({
  default: ({ enabledNestedBlock, enabledTextAlignment }) => (
    <div
      data-testid='blocknote-toolbar'
      data-nested-block={enabledNestedBlock}
      data-text-alignment={enabledTextAlignment}>
      Toolbar
    </div>
  ),
}));

vi.mock('../BlockNoteExtensions', () => ({
  default: { extension1: 'value1' },
}));

vi.mock('../CustomComponents', () => ({
  FilePanel: () => <div data-testid='file-panel'>File Panel</div>,
  LinkToolbar: props => <div data-testid='link-toolbar'>Link Toolbar</div>,
  SideMenu: ({ dragHandleMenu }) => <div data-testid='side-menu'>Side Menu</div>,
}));

vi.mock('../useBlockNoteStyles', () => ({
  useBlockNoteStyles: vi.fn(({ isBordered }) => ({
    classes: {
      editorContainer: 'editor-container',
      markdownEditor: 'markdown-editor',
    },
    cx: (...args) => args.filter(Boolean).join(' '),
  })),
}));

// First define the mock handler at file top level
const mockHandlePasteEvent = vi.fn();

vi.mock('../../hooks/useEditorPasteClipboard', () => ({
  useEditorPasteClipboard: vi.fn(() => ({
    handlePasteToEditorEvent: mockHandlePasteEvent,
  })),
}));

vi.mock('../../utils/string', () => ({
  normalizeMarkdownToBlockNote: vi.fn(str => `normalized-${str}`),
  finalizeMarkdownFromBlockNote: vi.fn(str => str),
}));

vi.mock('../utils', () => ({
  convertEmptyParagraphToSpaceContent: vi.fn(blocks => blocks),
  parseSpaceContentToEmptyParagraph: vi.fn(blocks => blocks),
}));

vi.mock('lodash/includes', () => ({
  __esModule: true,
  default: (str, search) => str && str.includes(search),
}));

vi.mock('lodash/isNil', () => ({
  __esModule: true,
  default: val => val === null || val === undefined,
}));

describe('BlockNoteMarkdown Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId } = render(<BlockNoteMarkdown />);
    expect(getByTestId('blocknote-view')).toBeDefined();
  });

  it('renders with default props correctly', () => {
    const { getByTestId } = render(<BlockNoteMarkdown />);
    const blockNoteView = getByTestId('blocknote-view');

    expect(blockNoteView.getAttribute('data-editable')).toBe('true');
    expect(blockNoteView.getAttribute('data-theme')).toBe('light');
    expect(blockNoteView.getAttribute('data-formatting-toolbar')).toBe('false');
    expect(blockNoteView.getAttribute('data-link-toolbar')).toBe('false');
    expect(blockNoteView.getAttribute('data-file-panel')).toBe('true');
    expect(blockNoteView.getAttribute('data-side-menu')).toBe('false');
  });

  it('renders with custom props correctly', () => {
    const { getByTestId } = render(
      <BlockNoteMarkdown
        isEditable={false}
        usingCustomFormattingToolbar={false}
        usingCustomLinkToolbar={false}
        usingCustomFilePanel={true}
      />
    );

    const blockNoteView = getByTestId('blocknote-view');
    expect(blockNoteView.getAttribute('data-editable')).toBe('false');
    expect(blockNoteView.getAttribute('data-formatting-toolbar')).toBe('true');
    expect(blockNoteView.getAttribute('data-link-toolbar')).toBe('true');
    expect(blockNoteView.getAttribute('data-file-panel')).toBe('false');
  });

  it('loads initial markdown correctly', async () => {
    // Set up the mock to resolve properly
    const mockBlocks = [{ id: '1', type: 'paragraph', content: 'Test' }];
    mockEditor.tryParseMarkdownToBlocks.mockResolvedValue(mockBlocks);

    // Clear previous calls
    mockEditor.tryParseMarkdownToBlocks.mockClear();
    mockEditor.replaceBlocks.mockClear();
    mockEditor.focus.mockClear();

    render(<BlockNoteMarkdown initialMarkdown='# Test Markdown' autoFocus={true} />);

    // Wait for promises to resolve using waitFor
    await vi.waitFor(() => {
      expect(mockEditor.tryParseMarkdownToBlocks).toHaveBeenCalled();
    });

    // Now check that the subsequent methods were called
    await vi.waitFor(() => {
      expect(mockEditor.replaceBlocks).toHaveBeenCalled();
    });

    // For focus, we need to check if autoFocus was enabled
    // The focus might be called via requestAnimationFrame, so let's wait a bit longer
    await vi.waitFor(
      () => {
        expect(mockEditor.focus).toHaveBeenCalled();
      },
      { timeout: 1000 }
    );
  });

  it('calls onChange when editor content changes', async () => {
    const onChange = vi.fn();
    render(<BlockNoteMarkdown onChange={onChange} />);

    // Manually trigger the handleEditorChange function by simulating the editor change
    // First get a reference to the mock editor's onChange handler
    const mockOnChange = vi
      .mocked(mockEditor._tiptapEditor.on)
      .mock.calls.find(call => call[0] === 'update');

    // If the onChange handler exists, call it
    if (mockOnChange && typeof mockOnChange[1] === 'function') {
      mockOnChange[1]();
    } else {
      // Otherwise directly call the blocksToMarkdownLossy method
      mockEditor.blocksToMarkdownLossy();
    }

    // Since onChange works with async functions and promises, we need to wait
    await vi.waitFor(() => {
      expect(mockEditor.blocksToMarkdownLossy).toHaveBeenCalled();
    });
  });

  it('renders with different language correctly', () => {
    const { getByTestId } = render(<BlockNoteMarkdown language='fr' />);

    // Instead of checking a specific element, verify the editor was created with the right dictionary
    expect(mockEditor.dictionary).toEqual({ drag_handle: { delete_menuitem: 'Supprimer' } });
  });

  it('renders custom components based on props', () => {
    const { getByTestId } = render(
      <BlockNoteMarkdown
        usingCustomFormattingToolbar={true}
        usingCustomLinkToolbar={true}
        usingCustomFilePanel={true}
      />
    );

    expect(getByTestId('blocknote-toolbar')).toBeDefined();
    expect(getByTestId('file-panel-controller')).toBeDefined();
    expect(getByTestId('link-toolbar-controller')).toBeDefined();
    expect(getByTestId('side-menu-controller')).toBeDefined();
  });

  it('applies custom class names', () => {
    const { getByTestId } = render(<BlockNoteMarkdown className='custom-class' />);
    const blockNoteView = getByTestId('blocknote-view');

    expect(blockNoteView.className).toContain('custom-class');
    expect(blockNoteView.className).toContain('editor-container');
    expect(blockNoteView.className).toContain('markdown-editor');
  });
});
