import { useCallback, useEffect, useRef } from 'react';
import { useCreateBlockNote } from '@blocknote/react';
import { BaseEditorProps } from '../types/blocknote';
import BlockNoteLocales from '../components/BlockNoteLocales';
import BlockNoteExtensions from '../components/BlockNoteExtensions';
import { validateUploadFile } from '../utils';
import {
  hasMultipleNewLines,
  isMarkdownContent,
  normalizeMarkdownWhenPastingToEditor,
} from '../utils/string';

// Define the expected TipTap editor interface
export interface TipTapEditor {
  isFocused: boolean;
  on: (event: string, handler: any) => void;
  off: (event: string, handler: any) => void;
  commands: {
    insertContent: (content: any) => void;
    focus: () => void;
  };
}

/**
 * Props for the useBlockNoteEditor hook
 */
export type UseBlockNoteEditorProps = Pick<
  BaseEditorProps,
  'language' | 'autoFocus' | 'uploadFile' | 'onBlur' | 'onFocus'
> & {
  schema?: any; // Schema can be any BlockNote schema
  pasteHandler?: (context: {
    event: ClipboardEvent;
    editor: any;
    defaultPasteHandler: () => boolean | undefined;
  }) => boolean | undefined;
};

/**
 * Custom hook to handle common BlockNote editor functionality
 */
export const useBlockNoteEditor = ({
  schema,
  language = 'en',
  autoFocus = false,
  uploadFile,
  onBlur,
  onFocus,
  pasteHandler,
}: UseBlockNoteEditorProps) => {
  // Reference to the editor container element
  const editorRef = useRef<HTMLDivElement>(null);

  // Reference to store requestAnimationFrame ID for cleanup
  const rafRef = useRef<number>();

  // Default paste handler: preserves multi-line plain text and parses Markdown
  const defaultPasteHandler = useCallback(
    ({
      event,
      editor,
      defaultPasteHandler,
    }: {
      event: ClipboardEvent;
      editor: any;
      defaultPasteHandler: () => boolean | undefined;
    }) => {
      if (!event.clipboardData) return !!defaultPasteHandler();

      const plainText = event.clipboardData.getData('text/plain');
      if (!plainText) return !!defaultPasteHandler();

      const normalizedText = plainText.replace(/\r\n/g, '\n');

      // Heuristic: treat as markdown if contains markdown syntax or multi-line content
      const looksLikeMarkdown =
        hasMultipleNewLines(normalizedText) || isMarkdownContent(normalizedText);

      if (looksLikeMarkdown) {
        const md = normalizeMarkdownWhenPastingToEditor(normalizedText);
        editor.pasteMarkdown(md);
        return true;
      }

      // Plain multi-line text
      if (normalizedText.includes('\n')) {
        editor.pasteText(normalizedText);
        return true;
      }

      // Fallback to BlockNote's default behaviour
      return !!defaultPasteHandler();
    },
    [],
  );

  // Determine which paste handler to use
  const effectivePasteHandler = pasteHandler ?? defaultPasteHandler;

  // Create the BlockNote editor instance
  const editor = useCreateBlockNote({
    schema,
    dictionary: BlockNoteLocales[language],
    ...BlockNoteExtensions,
    pasteHandler: effectivePasteHandler,
    ...(uploadFile
      ? {
          uploadFile: async (file: File) => {
            if (!uploadFile || !validateUploadFile(file)) return '';
            return await uploadFile(file);
          },
        }
      : {}),
  });

  /**
   * Handles focus events on the editor
   * Ensures proper editor focus state
   */
  const handleAutoFocus = useCallback(() => {
    if (!(editor._tiptapEditor as unknown as TipTapEditor)?.isFocused && editorRef.current) {
      // Use requestAnimationFrame for better performance when focusing
      if (typeof window !== 'undefined') {
        rafRef.current = requestAnimationFrame(() => {
          editor.focus();
          rafRef.current = undefined;
        });
      } else {
        editor.focus();
      }
    }
  }, [editor]);

  /**
   * Checks if the target element is a checkbox within a checklist item
   */
  const isCheckboxClick = useCallback((target: EventTarget | null): boolean => {
    if (!target || !(target instanceof Element)) return false;

    // Check if the target is a checkbox input
    if (target.tagName === 'INPUT' && (target as HTMLInputElement).type === 'checkbox') {
      return true;
    }

    // Check if the target is within a checklist item container
    const checklistContainer = target.closest('[data-content-type="checkListItem"]');
    if (checklistContainer) {
      // Verify it contains a checkbox input
      const checkbox = checklistContainer.querySelector('input[type="checkbox"]');
      return !!checkbox;
    }

    return false;
  }, []);

  /**
   * Simplified blur handler that only manages the onBlur callback
   */
  const bindOnBlur = useCallback(
    context => {
      if (!onBlur || !editorRef.current) return;
      const { event } = context;

      // Only trigger onBlur when focus moves outside the editor
      const isTargetWithinEditor =
        event.relatedTarget && editorRef.current?.contains(event.relatedTarget);

      if (!isTargetWithinEditor) {
        onBlur();
      } else if (isCheckboxClick(event.relatedTarget)) {
        // Only apply handleAutoFocus for checkbox clicks in checklist items
        handleAutoFocus();
      }
    },
    [onBlur, handleAutoFocus, isCheckboxClick]
  );

  /**
   * Focus handler that calls the onFocus callback when the editor receives focus
   */
  const bindOnFocus = useCallback(() => {
    if (!onFocus) return;
    onFocus();
  }, [onFocus]);

  // Set up listeners for blur and focus events
  useEffect(() => {
    const tipTapEditor = editor._tiptapEditor as unknown as TipTapEditor;

    // Only bind events if the callbacks are provided
    if (onBlur) {
      tipTapEditor.on('blur', bindOnBlur);
    }

    if (onFocus) {
      tipTapEditor.on('focus', bindOnFocus);
    }

    // Auto-focus the editor if required
    if (autoFocus) {
      handleAutoFocus();
    }

    // Cleanup function to remove event listeners
    return () => {
      if (onBlur) {
        tipTapEditor.off('blur', bindOnBlur);
      }

      if (onFocus) {
        tipTapEditor.off('focus', bindOnFocus);
      }

      // Cancel any pending focus operations
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [editor, autoFocus, onBlur, onFocus, bindOnBlur, bindOnFocus, handleAutoFocus]);

  return {
    editor,
    editorRef,
    handleAutoFocus,
  };
};
