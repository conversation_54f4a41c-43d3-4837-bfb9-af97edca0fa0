import { useMantineTheme } from '@mantine/core';
import {
  Background,
  BackgroundVariant,
  ConnectionMode,
  Controls,
  MarkerType,
  ReactFlow,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import BuilderLayout from '@/components/BuilderLayout';
import { InnerVersionControl } from '@/components/BuilderLayout/VersionControlWrapper';
import CatalogModal from '@/components/FlowBuilder/ModalPlacements/CatalogModal';
import RightFormPanel from '@/components/FlowBuilder/ModalPlacements/RightFormPanel';
import UnsavedChangesModal from '@/components/UnsavedChangesModal';
import { FlowBuilderProvider, useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { VersionContextProvider } from '@/contexts/VersionContext';
import { useUnsavedChanges } from '@/hooks/useUnsavedChanges';
import nodeTypes from './nodeTypes';
import RunWrapper from '@/components/BuilderLayout/RunWrapper';
import { RunContextProvider, useRunContext } from '@/contexts/RunContext';

const defaultEdgeOptions = {
  type: 'floating',
  markerEnd: {
    type: MarkerType.ArrowClosed,
  },
};

function FlowBuilderContent() {
  const {
    flow,
    flowOriginal,
    nodes,
    edges,
    loading,
    handleRun,
    handleBack,
    closeCatalog,
    catalogModalRef,
    openedRightPanel,
    handleDeleteFlow,
    handlePublish,
    handleTitleChange,
    handleDuplicateFlow,
    handleSaveAsTemplate,
    // handleOpenTemplate,
    handleOnSelectCatalog,
    handleCloseRightPanel,
  } = useFlowBuilderContext();
  const { workspaceId } = useParams();
  const navigate = useNavigate();
  const theme = useMantineTheme();
  const { mutateRuns } = useRunContext();

  const initialFitViewNodes = useMemo(() => {
    // select all trigger nodes's id and 2 first nodes's id after trigger
    if (!flow?.triggers) return [];
    const triggerKeys = Object.keys(flow?.triggers);
    const nextNodeId = flow?.triggers?.[triggerKeys[0]]?.next ?? '';
    const nextNextNodeId = flow?.nodes?.[nextNodeId]?.next ?? '';
    return [
      ...triggerKeys.map(key => {
        return {
          id: key,
        };
      }),
      {
        id: nextNodeId,
      },
      {
        id: nextNextNodeId,
      },
    ];
  }, [flow?.triggers]);

  const [unChanged, setUnChanged] = useState(false);

  const { showPrompt, confirmNavigation, cancelNavigation } = useUnsavedChanges({
    when: unChanged,
    onConfirm: () => {
      setUnChanged(false);
    },
  });

  const handlePublishFlow = () => {
    setUnChanged(false);
    handlePublish();
  };

  const handleRunManual = async () => {
    await handleRun();
    mutateRuns();
  };

  const goBack = () => {
    navigate(`/studio/${workspaceId}/flows`);
  };

  useEffect(() => {
    if (!flow) return;
    const hasChanged = JSON.stringify(flow) !== JSON.stringify(flowOriginal);
    setUnChanged(hasChanged);
  }, [flow, flowOriginal]);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <BuilderLayout
        onBack={handleBack}
        title={flow?.name || 'Untitled Flow'}
        maxTitleLength={40}
        onTitleChange={handleTitleChange}
        onPublish={handlePublishFlow}
        onRun={handleRunManual}
        // openTemplate={handleOpenTemplate}
        versionControl={<InnerVersionControl resourceId={flow?.id || ''} resourceType='flow' />}
        historyRuns={<RunWrapper resourceId={flow?.id || ''} />}
        seeMoreActions={{
          onDuplicate: () => {
            flow && handleDuplicateFlow(flow, false);
          },
          onDelete: () => {
            flow && handleDeleteFlow(flow, () => goBack());
          },
          onSaveAsTemplate: handleSaveAsTemplate,
        }}>
        <ReactFlow
          fitView
          nodes={nodes}
          edges={edges}
          minZoom={0.4}
          maxZoom={1.5}
          nodeTypes={nodeTypes}
          fitViewOptions={{
            padding: 0.2,
            minZoom: 1.2,
            maxZoom: 1.2,
            includeHiddenNodes: false,
            nodes: initialFitViewNodes,
          }}
          deleteKeyCode={null}
          connectionMode={ConnectionMode.Loose}
          defaultEdgeOptions={defaultEdgeOptions}
          defaultViewport={{ x: 0, y: 0, zoom: 1.2 }}>
          <Background
            color={theme.colors.silverFox[6]}
            bgColor={theme.colors.silverFox[2]}
            gap={[18, 18]}
            size={2}
            variant={BackgroundVariant.Dots}
          />
          <Controls showInteractive={false} />
        </ReactFlow>
        <RightFormPanel opened={openedRightPanel} onClose={handleCloseRightPanel} />

        <UnsavedChangesModal
          opened={showPrompt}
          onCancel={cancelNavigation}
          onConfirm={confirmNavigation}
        />
      </BuilderLayout>
      <CatalogModal ref={catalogModalRef} onClose={closeCatalog} onSelect={handleOnSelectCatalog} />
    </>
  );
}

export default function FlowBuilder() {
  const { flowId } = useParams();

  return (
    <FlowBuilderProvider>
      <RunContextProvider resourceType='flow' resourceId={flowId || ''}>
        <VersionContextProvider resourceType='flow' resourceId={flowId || ''}>
          <FlowBuilderContent />
        </VersionContextProvider>
      </RunContextProvider>
    </FlowBuilderProvider>
  );
}
