import { MantineWrapper, mockLibraries, renderWithMantine } from '@/utils/test';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import Credentials from '.';
import type { CredentialContextType } from '@/contexts/CredentialContext';
import { LayoutType } from '@/types';
import { AppContextProvider } from '@/contexts/AppContext';
import { act } from '@testing-library/react';
import { mockTools, mockCredentials } from '@/mockdata/credential';
import { handleWithError } from './index';

mockLibraries();
vi.mock('lodash/throttle', () => ({
  __esModule: true,
  default: vi.fn((fn: (...args: any[]) => void) => fn),
}));

vi.mock('@resola-ai/ui', () => ({
  CredentialModalTolgee: ({
    children,
    onSubmit,
    onTest,
    onDelete,
    credential,
    schema,
    loading,
  }: any) => (
    <div data-testid='credential-modal'>
      {children}
      <button data-testid='modal-submit' onClick={() => onSubmit?.({ name: 'test' })}>
        Submit
      </button>
      <button data-testid='modal-test' onClick={() => onTest?.({ name: 'test' })}>
        Test
      </button>
      <button data-testid='modal-delete' onClick={() => onDelete?.({ id: '1', name: 'test' })}>
        Delete
      </button>
      <div data-testid='credential-data'>{credential?.name || 'No credential'}</div>
      <div data-testid='schema-data'>{schema?.name || 'No schema'}</div>
      <div data-testid='loading-state'>{loading ? 'Loading' : 'Not loading'}</div>
    </div>
  ),
  DecaButton: ({ children, ...props }: { children: React.ReactNode } & any) => (
    <button {...props}>{children}</button>
  ),
  DecaCheckbox: ({ checked, onChange, label, 'data-testid': dataTestId, style }: any) => (
    <div data-testid={dataTestId || 'deca-checkbox'} style={style}>
      <input type='checkbox' checked={checked} onChange={onChange} data-testid='checkbox-input' />
      <span>{label}</span>
    </div>
  ),
  credentialNodes: [],
  CatalogNodeIcon: ({ name }: { name: string }) => <span data-testid={`icon-${name}`}>{name}</span>,
}));

vi.mock('@/components/AIEmpty', () => ({
  __esModule: true,
  default: () => <div data-testid='ai-empty'>AIEmpty</div>,
}));

// Mock the AppContext to handle the ConfirmModal
vi.mock('@/contexts/AppContext', () => {
  const originalModule = vi.importActual('@/contexts/AppContext');
  return {
    ...originalModule,
    useAppContext: () => ({
      openConfirmModal: ({ title, content, confirmText, onConfirm, onCancel }: any) => {
        // Immediately render a mock modal
        const modal = document.createElement('div');
        modal.setAttribute('data-testid', 'confirm-modal');

        const titleEl = document.createElement('div');
        titleEl.textContent = title;
        modal.appendChild(titleEl);

        const contentEl = document.createElement('div');
        contentEl.textContent = typeof content === 'string' ? content : 'Content';
        modal.appendChild(contentEl);

        const checkbox = document.createElement('div');
        checkbox.setAttribute('data-testid', 'checkbox-confirm-modal');
        checkbox.textContent = 'Checkbox';
        modal.appendChild(checkbox);

        const confirmButton = document.createElement('button');
        confirmButton.textContent = confirmText;
        confirmButton.onclick = () => onConfirm('');
        modal.appendChild(confirmButton);

        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Cancel';
        cancelButton.onclick = () => onCancel?.();
        modal.appendChild(cancelButton);

        document.body.appendChild(modal);
      },
      closeConfirmModal: vi.fn(),
      confirmModal: { opened: false },
    }),
    AppContextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  };
});

describe('Credentials', () => {
  const defaultCredentialContextValue: CredentialContextType = {
    credentials: {
      data: mockCredentials,
      nextCursor: '',
      prevCursor: '',
    },
    tools: mockTools,
    limit: 10,
    setLimit: vi.fn(),
    cursor: '',
    setCursor: vi.fn(),
    searchValue: '',
    setSearchValue: vi.fn(),
    loading: false,
    createCredential: vi.fn(),
    updateCredential: vi.fn(),
    deleteCredential: vi.fn(),
    testCredential: vi.fn(),
    reconnectCredential: vi.fn(),
    selectedCredential: undefined,
    setSelectedCredential: vi.fn(),
    selectedTool: undefined,
    setSelectedTool: vi.fn(),
    loadingCredential: false,
    fetchCredential: vi.fn(),
  };

  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    return (
      <MantineWrapper>
        <BrowserRouter>
          <AppContextProvider>{children}</AppContextProvider>
        </BrowserRouter>
      </MantineWrapper>
    );
  };

  const renderWithProvider = (value?: Partial<CredentialContextType>) => {
    return renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <Credentials
            value={{
              ...defaultCredentialContextValue,
              ...value,
            }}
          />
        </AppContextProvider>
      </BrowserRouter>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Clean up any existing modals
    const existingModals = document.querySelectorAll('[data-testid="confirm-modal"]');
    existingModals.forEach((modal) => modal.remove());
  });

  afterEach(() => {
    // Clean up any remaining modals
    const existingModals = document.querySelectorAll('[data-testid="confirm-modal"]');
    existingModals.forEach((modal) => modal.remove());
  });

  it('renders without crashing', async () => {
    const { rerender } = renderWithProvider();
    rerender(
      <Wrapper>
        <Credentials value={defaultCredentialContextValue} />
      </Wrapper>
    );

    const grid = await screen.findByTestId('grid-layout');
    expect(grid).toBeInTheDocument();
    expect(screen.getByTestId('credentials-page')).toBeInTheDocument();
  });

  it('displays page header with correct elements', () => {
    renderWithProvider();
    expect(screen.getByTestId('page-title')).toBeInTheDocument();
    expect(screen.getByText('list.title')).toBeInTheDocument();
    expect(screen.getByText('list.description')).toBeInTheDocument();
    expect(screen.getByTestId('search-input')).toBeInTheDocument();
  });

  it('displays credentials list when credentials exist', async () => {
    renderWithProvider();
    const grid = await screen.findByTestId('grid-layout');
    expect(grid).toBeInTheDocument();
  });

  it('displays empty list when no credentials exist', async () => {
    renderWithProvider({
      credentials: { data: [], nextCursor: '', prevCursor: '' },
    });
    expect(screen.getByTestId('ai-empty')).toBeInTheDocument();
  });

  it('handles search input change', async () => {
    const setSearchValue = vi.fn();

    renderWithProvider({ setSearchValue });

    const inputElement = await screen.findByPlaceholderText('list.search');
    fireEvent.change(inputElement, { target: { value: 'test' } });
    await waitFor(() => {
      expect(setSearchValue).toHaveBeenCalledWith('test');
    });
  });

  it('handles layout change', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const segmentedControl = screen.getByTestId('layout-control');
    const listOption = segmentedControl.querySelector(`[value="${LayoutType.LIST}"]`);

    if (listOption) {
      await act(async () => {
        await user.click(listOption);
      });
      const gridLayout = screen.getByTestId('grid-layout');
      expect(gridLayout).toHaveStyle({ width: '100%' });
    }
  });

  it('opens tools modal when create credential button is clicked', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const createButton = screen.getByText('action.createCredential');
    await act(async () => {
      await user.click(createButton);
    });
    const ProvidersModal = await screen.findByText('tool.modal.title');
    expect(ProvidersModal).toBeInTheDocument();
  });

  it('calls deleteCredential when delete action is triggered', async () => {
    const deleteCredential = vi.fn();
    renderWithProvider({ deleteCredential });
    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    expect(menuButton).toBeInTheDocument();
    await user.click(menuButton);
    const deleteAction = await screen.findByText('action.delete');
    await user.click(deleteAction);
    const deleteModal = await screen.findByText('modalDelete.title');
    expect(deleteModal).toBeInTheDocument();

    // Find the checkbox and click it to enable the confirm button
    const checkboxes = screen.getAllByTestId('checkbox-confirm-modal');
    await user.click(checkboxes[0]);

    const confirmButton = screen.getByText('modalDelete.confirmText');
    await user.click(confirmButton);
    expect(deleteCredential).toHaveBeenCalledWith('1');
  });

  it('calls testCredential when test action is triggered', async () => {
    const testCredential = vi.fn();
    renderWithProvider({ testCredential });
    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    expect(menuButton).toBeInTheDocument();
    await user.click(menuButton);
    const testAction = await screen.findByText('action.test');
    await user.click(testAction);
    await waitFor(() => {
      expect(testCredential).toHaveBeenCalledWith(mockCredentials[0]);
    });
  });

  it('calls reconnectCredential when reconnect action is triggered', async () => {
    const reconnectCredential = vi.fn();
    renderWithProvider({ reconnectCredential });
    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    expect(menuButton).toBeInTheDocument();
    await user.click(menuButton);
    const reconnectAction = await screen.findByText('action.reconnect');
    await user.click(reconnectAction);
    const reconnectModal = await screen.findByText('modalReconnect.title');
    expect(reconnectModal).toBeInTheDocument();

    // Find the checkbox and click it to enable the confirm button
    const checkboxes = screen.getAllByTestId('checkbox-confirm-modal');
    await user.click(checkboxes[0]);

    const confirmButton = screen.getByText('modalReconnect.confirmText');
    await user.click(confirmButton);
    expect(reconnectCredential).toHaveBeenCalledWith('1');
  });

  it('fetches credentials on component mount', async () => {
    renderWithProvider();
    const grid = await screen.findByTestId('grid-layout');
    expect(grid).toBeInTheDocument();
  });

  it('handles pagination changes', async () => {
    renderWithProvider();
    const paginationButtons = screen.getAllByTestId(/aip-(previous|next)-button/);
    expect(paginationButtons.length).toBeGreaterThan(0);
  });

  it('handles per page changes', async () => {
    renderWithProvider();
    const perPageSelect = screen.getByTestId('aip-per-page-select');
    expect(perPageSelect).toBeInTheDocument();
  });

  it('fetches credential when a credential is selected', async () => {
    const fetchCredential = vi.fn();
    renderWithProvider({ fetchCredential });
    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));
    expect(fetchCredential).toHaveBeenCalledWith(credential.id);
  });

  // New tests for 100% coverage

  it('handles credential selection when tool is not found - direct test', async () => {
    const setSelectedTool = vi.fn();
    const fetchCredential = vi.fn();
    const setSelectedCredential = vi.fn();

    // Create a credential with a provider that doesn't exist in tools
    const credentialWithUnknownProvider = {
      ...mockCredentials[0],
      provider: 'UNKNOWN_PROVIDER',
    };

    renderWithProvider({
      setSelectedTool,
      fetchCredential,
      setSelectedCredential,
      credentials: {
        data: [credentialWithUnknownProvider],
        nextCursor: '',
        prevCursor: '',
      },
    });

    const user = userEvent.setup();
    await user.click(screen.getByText(credentialWithUnknownProvider.name));

    // Should not call setSelectedTool or fetchCredential when tool is not found
    expect(setSelectedTool).not.toHaveBeenCalled();
    expect(fetchCredential).not.toHaveBeenCalled();
    expect(setSelectedCredential).not.toHaveBeenCalled();
  });

  it('handles handleWithError with setStatus callback for error', async () => {
    const setStatus = vi.fn();
    const errorCallback = vi.fn().mockRejectedValue(new Error('Test error'));

    await handleWithError(errorCallback, setStatus);

    expect(setStatus).toHaveBeenCalledWith({
      status: 'error',
      message: 'Test error',
    });
  });

  it('handles handleWithError with setStatus callback for non-Error object', async () => {
    const setStatus = vi.fn();
    const errorCallback = vi.fn().mockRejectedValue('String error');

    await handleWithError(errorCallback, setStatus);

    expect(setStatus).toHaveBeenCalledWith({
      status: 'error',
      message: 'An unknown error occurred',
    });
  });

  it('handles handleWithError with setStatus callback for success', async () => {
    const setStatus = vi.fn();
    const successCallback = vi.fn().mockResolvedValue(undefined);

    await handleWithError(successCallback, setStatus);

    expect(setStatus).not.toHaveBeenCalled();
  });

  it('handles handleWithError without setStatus callback for error', async () => {
    const errorCallback = vi.fn().mockRejectedValue(new Error('Test error'));

    // Should not throw even without setStatus
    await expect(handleWithError(errorCallback)).resolves.toBeUndefined();
  });

  it('handles handleWithError without setStatus callback for success', async () => {
    const successCallback = vi.fn().mockResolvedValue(undefined);

    await expect(handleWithError(successCallback)).resolves.toBeUndefined();
  });

  it('handles credential selection with empty tools array', async () => {
    const setSelectedTool = vi.fn();
    const fetchCredential = vi.fn();

    renderWithProvider({
      setSelectedTool,
      fetchCredential,
      tools: [], // Empty tools array
    });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Should not call setSelectedTool or fetchCredential when tools array is empty
    expect(setSelectedTool).not.toHaveBeenCalled();
    expect(fetchCredential).not.toHaveBeenCalled();
  });

  it('handles credential selection with undefined tools', async () => {
    const setSelectedTool = vi.fn();
    const fetchCredential = vi.fn();

    renderWithProvider({
      setSelectedTool,
      fetchCredential,
      tools: undefined, // Undefined tools
    });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Should not call setSelectedTool or fetchCredential when tools is undefined
    expect(setSelectedTool).not.toHaveBeenCalled();
    expect(fetchCredential).not.toHaveBeenCalled();
  });

  it('handles credential selection', async () => {
    const setSelectedTool = vi.fn();
    const fetchCredential = vi.fn();
    const setSelectedCredential = vi.fn();

    renderWithProvider({
      setSelectedTool,
      fetchCredential,
      setSelectedCredential,
      tools: mockTools,
    });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    expect(setSelectedTool).toHaveBeenCalledWith(
      expect.objectContaining({
        name: credential.provider,
      })
    );
    expect(fetchCredential).toHaveBeenCalledWith(credential.id);
  });

  it('handles edit credential action', async () => {
    const updateCredential = vi.fn();
    renderWithProvider({ updateCredential });
    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);
    const editAction = await screen.findByText('action.edit');
    await user.click(editAction);

    // The edit action should trigger the credential modal
    expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
  });

  it('handles create credential through modal', async () => {
    const createCredential = vi.fn();

    renderWithProvider({
      createCredential,
      selectedCredential: undefined, // No selected credential means create mode
    });

    // Open the credential modal by selecting a credential
    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Submit the modal
    const submitButton = screen.getByTestId('modal-submit');
    await user.click(submitButton);

    expect(createCredential).toHaveBeenCalledWith({ name: 'test' });
  });

  it('handles update credential through modal', async () => {
    const updateCredential = vi.fn();
    const selectedCredential = {
      id: '1',
      name: 'Test Credential',
      provider: 'GOOGLE',
      authenticationScheme: 'API_KEY',
      settings: {},
      workspaceId: '1',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    renderWithProvider({
      updateCredential,
      selectedCredential,
    });

    // Open the credential modal by selecting a credential
    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Submit the modal
    const submitButton = screen.getByTestId('modal-submit');
    await user.click(submitButton);

    expect(updateCredential).toHaveBeenCalledWith({ name: 'test' });
  });

  it('handles test credential through modal', async () => {
    const testCredential = vi.fn();

    renderWithProvider({ testCredential });

    // Open the credential modal by selecting a credential
    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Test the credential
    const testButton = screen.getByTestId('modal-test');
    await user.click(testButton);

    expect(testCredential).toHaveBeenCalledWith({ name: 'test' });
  });

  it('handles delete credential through modal', async () => {
    const deleteCredential = vi.fn();

    renderWithProvider({ deleteCredential });

    // Open the credential modal by selecting a credential
    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Delete the credential
    const deleteButton = screen.getByTestId('modal-delete');
    await user.click(deleteButton);

    // check modal delete is open
    const modalDelete = await screen.findByText('modalDelete.title');
    expect(modalDelete).toBeInTheDocument();

    const checkboxes = screen.getAllByTestId('checkbox-confirm-modal');
    await user.click(checkboxes[0]);

    const confirmButton = screen.getByText('modalDelete.confirmText');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(deleteCredential).toHaveBeenCalledWith('1');
    });
  });

  it('handles error in handleWithError function', async () => {
    const testCredential = vi.fn().mockRejectedValue(new Error('Test error'));
    renderWithProvider({ testCredential });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Test the credential which should trigger the error handling
    const testButton = screen.getByTestId('modal-test');
    await user.click(testButton);

    // The error should be handled gracefully
    await waitFor(() => {
      expect(testCredential).toHaveBeenCalled();
    });
  });

  it('handles error with non-Error object in handleWithError', async () => {
    const testCredential = vi.fn().mockRejectedValue('String error');
    renderWithProvider({ testCredential });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Test the credential which should trigger the error handling
    const testButton = screen.getByTestId('modal-test');
    await user.click(testButton);

    // The error should be handled gracefully
    await waitFor(() => {
      expect(testCredential).toHaveBeenCalled();
    });
  });

  it('handles delete credential with no id', async () => {
    const deleteCredential = vi.fn();

    // Create a credential without id
    const credentialWithoutId = { ...mockCredentials[0], id: '' };

    renderWithProvider({
      credentials: {
        data: [credentialWithoutId],
        nextCursor: '',
        prevCursor: '',
      },
      deleteCredential,
    });

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);
    const deleteAction = await screen.findByText('action.delete');
    await user.click(deleteAction);

    const checkboxes = screen.getAllByTestId('checkbox-confirm-modal');
    await user.click(checkboxes[0]);

    const confirmButton = screen.getByText('modalDelete.confirmText');
    await user.click(confirmButton);

    // Should not call deleteCredential when id is empty
    expect(deleteCredential).not.toHaveBeenCalled();
  });

  it('handles cancel in confirm modal', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);
    const deleteAction = await screen.findByText('action.delete');
    await user.click(deleteAction);

    const cancelButtons = screen.getAllByText('Cancel');
    await user.click(cancelButtons[0]);
    // Simulate modal close
    const modals = document.querySelectorAll('[data-testid="confirm-modal"]');
    modals.forEach((m) => m.remove());
    expect(screen.queryByTestId('confirm-modal')).not.toBeInTheDocument();
  });

  it('renders CredentialsPage wrapper component', () => {
    const { container } = renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <Credentials value={defaultCredentialContextValue} />
        </AppContextProvider>
      </BrowserRouter>
    );

    expect(container).toBeInTheDocument();
  });

  it('displays credential modal with correct props', async () => {
    const selectedCredential = {
      id: '1',
      name: 'Test Credential',
      provider: 'GOOGLE',
      authenticationScheme: 'API_KEY',
      settings: {},
      workspaceId: '1',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    const selectedTool = { name: 'test-tool' };
    const loadingCredential = true;

    renderWithProvider({
      selectedCredential,
      selectedTool,
      loadingCredential,
    });

    // Open the modal by selecting a credential
    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Check that the modal displays the correct data
    expect(screen.getByTestId('credential-data')).toHaveTextContent('Test Credential');
    expect(screen.getByTestId('schema-data')).toHaveTextContent('test-tool');
    expect(screen.getByTestId('loading-state')).toHaveTextContent('Loading');
  });

  it('displays credential modal without credential data', async () => {
    renderWithProvider({
      selectedCredential: undefined,
      selectedTool: undefined,
      loadingCredential: false,
    });

    // Open the modal by selecting a credential
    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Check that the modal displays default values
    expect(screen.getByTestId('credential-data')).toHaveTextContent('No credential');
    expect(screen.getByTestId('schema-data')).toHaveTextContent('No schema');
    expect(screen.getByTestId('loading-state')).toHaveTextContent('Not loading');
  });

  it('handles setStatus callback in handleWithError for success', async () => {
    const testCredential = vi.fn().mockResolvedValue(undefined);
    renderWithProvider({ testCredential });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Test the credential which should trigger success status
    const testButton = screen.getByTestId('modal-test');
    await user.click(testButton);

    // The success should be handled and setStatus called
    await waitFor(() => {
      expect(testCredential).toHaveBeenCalled();
    });
  });

  it('handles setStatus callback in handleWithError for error', async () => {
    const testCredential = vi.fn().mockRejectedValue(new Error('Test error'));
    renderWithProvider({ testCredential });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Test the credential which should trigger error status
    const testButton = screen.getByTestId('modal-test');
    await user.click(testButton);

    // The error should be handled and setStatus called with error
    await waitFor(() => {
      expect(testCredential).toHaveBeenCalled();
    });
  });

  it('handles setStatus callback in handleWithError for non-Error object', async () => {
    const testCredential = vi.fn().mockRejectedValue('String error');
    renderWithProvider({ testCredential });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    // Test the credential which should trigger error status with string error
    const testButton = screen.getByTestId('modal-test');
    await user.click(testButton);

    // The error should be handled and setStatus called with string error
    await waitFor(() => {
      expect(testCredential).toHaveBeenCalled();
    });
  });

  it('handles setStatus callback in handleWithError for reconnect success', async () => {
    const reconnectCredential = vi.fn().mockResolvedValue(undefined);
    renderWithProvider({ reconnectCredential });

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);
    const reconnectAction = await screen.findByText('action.reconnect');
    await user.click(reconnectAction);

    const checkboxes = screen.getAllByTestId('checkbox-confirm-modal');
    await user.click(checkboxes[0]);

    const confirmButton = screen.getByText('modalReconnect.confirmText');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(reconnectCredential).toHaveBeenCalledWith('1');
    });
  });

  it('handles setStatus callback in handleWithError for reconnect error', async () => {
    const reconnectCredential = vi.fn().mockRejectedValue(new Error('Reconnect failed'));
    renderWithProvider({ reconnectCredential });

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);
    const reconnectAction = await screen.findByText('action.reconnect');
    await user.click(reconnectAction);

    const checkboxes = screen.getAllByTestId('checkbox-confirm-modal');
    await user.click(checkboxes[0]);

    const confirmButton = screen.getByText('modalReconnect.confirmText');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(reconnectCredential).toHaveBeenCalledWith('1');
    });
  });

  it('handles setStatus callback in handleWithError for create credential success', async () => {
    const createCredential = vi.fn().mockResolvedValue(undefined);
    renderWithProvider({ createCredential });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    const submitButton = screen.getByTestId('modal-submit');
    await user.click(submitButton);

    await waitFor(() => {
      expect(createCredential).toHaveBeenCalledWith({ name: 'test' });
    });
  });

  it('handles setStatus callback in handleWithError for create credential error', async () => {
    const createCredential = vi.fn().mockRejectedValue(new Error('Create failed'));
    renderWithProvider({ createCredential });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    const submitButton = screen.getByTestId('modal-submit');
    await user.click(submitButton);

    await waitFor(() => {
      expect(createCredential).toHaveBeenCalledWith({ name: 'test' });
    });
  });

  it('handles setStatus callback in handleWithError for update credential success', async () => {
    const updateCredential = vi.fn().mockResolvedValue(undefined);
    const selectedCredential = {
      id: '1',
      name: 'Test Credential',
      provider: 'GOOGLE',
      authenticationScheme: 'API_KEY',
      settings: {},
      workspaceId: '1',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    renderWithProvider({ updateCredential, selectedCredential });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    const submitButton = screen.getByTestId('modal-submit');
    await user.click(submitButton);

    await waitFor(() => {
      expect(updateCredential).toHaveBeenCalledWith({ name: 'test' });
    });
  });

  it('handles setStatus callback in handleWithError for update credential error', async () => {
    const updateCredential = vi.fn().mockRejectedValue(new Error('Update failed'));
    const selectedCredential = {
      id: '1',
      name: 'Test Credential',
      provider: 'GOOGLE',
      authenticationScheme: 'API_KEY',
      settings: {},
      workspaceId: '1',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    renderWithProvider({ updateCredential, selectedCredential });

    const user = userEvent.setup();
    const credential = mockCredentials[0];
    await user.click(screen.getByText(credential.name));

    const submitButton = screen.getByTestId('modal-submit');
    await user.click(submitButton);

    await waitFor(() => {
      expect(updateCredential).toHaveBeenCalledWith({ name: 'test' });
    });
  });

  it('handleWithError does not throw if setStatus is not provided', async () => {
    const fn = vi.fn().mockRejectedValue(new Error('fail'));
    await expect(handleWithError(fn)).resolves.toBeUndefined();
  });
});
