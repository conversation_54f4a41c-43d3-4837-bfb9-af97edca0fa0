import {
  type ConnectionData,
  type Flow,
  type FlowNodeData,
  FlowNodeSetting,
  FlowNodeType,
  type FlowRouteByIntentSetting,
} from '@/models/flow';
import { ulid } from 'ulid';

type TriggerInitDataType = { name?: FlowNodeType; displayName?: string; icon: string };

const getNodeInfo = (flow: Flow, id: string) => {
  let node: FlowNodeData | undefined;
  let isTriggerNode = false;
  let isBranchNode = false;
  let isLoopingNode = false;
  if (flow.triggers?.[id]) {
    // this connection is from trigger node to action or path nodes later.
    node = flow?.triggers?.[id];
    isTriggerNode = true;
  } else {
    node = flow?.nodes?.[id];
    isBranchNode = node?.name === FlowNodeType.Branch || node?.name === FlowNodeType.Path;
    isLoopingNode = node?.name === FlowNodeType.Looping;
  }
  return {
    node: node,
    isTriggerNode,
    isBranchNode,
    isLoopingNode,
  };
};

const checkAncestorIsLoopingNode = (
  flow: Flow,
  id: string
): { isLoopingNode: boolean; isConnectFromLoopSubNodes: boolean } => {
  let isLoopingNode = false;
  let isConnectFromLoopSubNodes = false;

  let currNodeId = id;
  let preNodeId = currNodeId;
  let currentNode = flow?.nodes?.[id];

  if (currentNode?.name === FlowNodeType.Looping) {
    isLoopingNode = true;
    return { isLoopingNode, isConnectFromLoopSubNodes };
  }

  while (currentNode) {
    if (currNodeId !== preNodeId && currentNode.name === FlowNodeType.Looping) {
      isLoopingNode = true;
      isConnectFromLoopSubNodes = currentNode?.settings?.nextChildID === preNodeId;
      break;
    }
    preNodeId = currNodeId;
    currNodeId = currentNode.prev ?? '';
    currentNode = flow?.nodes?.[currNodeId];
  }
  return { isLoopingNode, isConnectFromLoopSubNodes };
};

const getPreviousNodes = (flow: Flow, nodeId: string) => {
  const previousNodes: FlowNodeData[] = [];
  if (flow?.triggers?.[nodeId] || !flow?.nodes?.[nodeId]) return previousNodes;
  const newFlow = newCopy(flow);
  const nodes = { ...newFlow.triggers, ...newFlow.nodes };
  let currentNode = nodes?.[nodes?.[nodeId]?.prev || ''];

  while (currentNode) {
    // if the current node is one of trigger nodes, push all triggers to previousNodes
    if (newFlow?.triggers?.[currentNode.id]) {
      previousNodes.push(...Object.values(newFlow.triggers));
      break; // this means we reach to the top of the tree.
    }
    previousNodes.push(currentNode);
    const prevNodeId = currentNode.prev ?? '';
    currentNode = nodes?.[prevNodeId];
  }
  return previousNodes;
};

const addNodeToNodesFlow = (flow: Flow, node: FlowNodeData) => {
  flow.nodes = { ...flow.nodes, [node.id]: node };
};

const updateNextIdAllTriggerNodes = (flow: Flow, nextNodeId: string) => {
  Object.values(flow.triggers).forEach((triggerNode) => {
    triggerNode.next = nextNodeId;
  });
};

const getNewTriggerNodeData = (nextNodeId: string, initData: TriggerInitDataType) => {
  return {
    id: ulid(),
    name: initData.name || FlowNodeType.WebhookTrigger,
    displayName: initData.displayName,
    icon: initData.icon || '',
    description: '',
    settings: {},
    next: nextNodeId,
  } as unknown as FlowNodeData;
};

const getNewNodeData = (nextNodeId: string, parentId: string) => {
  // This suppose to have some additional property later.
  return {
    id: ulid(),
    name: FlowNodeType.EmptyNode,
    displayName: '',
    description: '',
    settings: {},
    next: nextNodeId,
    prev: parentId,
  } as unknown as FlowNodeData;
};

const getNewLoopingNode = (nextNodeId: string, parentId: string) => {
  return {
    id: ulid(),
    name: FlowNodeType.Looping,
    displayName: '',
    description: '',
    settings: {
      nextChildID: '',
    },
    next: nextNodeId, // as when introducing new looping node, the current nodes behind automatically become the nodes belong to the looping node
    prev: parentId,
  } as unknown as FlowNodeData;
};

const getNewBranchNode = (nextNodeId: string, parentId: string) => {
  return {
    id: ulid(),
    name: FlowNodeType.Path,
    displayName: '',
    description: '',
    settings: {
      paths: [
        {
          id: ulid(),
          settings: {},
          next: nextNodeId,
        },
        {
          id: ulid(),
          settings: {},
          next: '',
        },
      ],
    },
    prev: parentId,
  } as unknown as FlowNodeData;
};

const newCopy: <T>(object: T) => T = (object) => {
  return JSON.parse(JSON.stringify(object));
};

function useFlowActions() {
  // This hooks will handle all actions related to flow
  // Add node (triggers, nodes, paths, subpaths)
  // Remove node( trigger, node, paths, subpaths)

  const addNewTrigger = (
    flow: Flow,
    triggerInitData: { name?: FlowNodeType; displayName?: string; icon: string }
  ) => {
    // Add new trigger
    if (!flow) return undefined;
    const copyFlow = newCopy(flow)!;
    const nextNodeId = Object.values(copyFlow.triggers)?.[0]?.next ?? '';
    const newTriggerNode: FlowNodeData = getNewTriggerNodeData(nextNodeId, triggerInitData);
    copyFlow.triggers[newTriggerNode.id] = newTriggerNode;
    return copyFlow;
  };

  const addNewNode = (flow: Flow, connectionData: ConnectionData) => {
    // Add new node
    if (!flow) return undefined;
    const copyFlow = newCopy(flow)!;
    const { parentId, nextNodeId, directSubNodeLooping } = connectionData;
    const {
      node: parentNode,
      isTriggerNode: isParentTriggerNode,
      isBranchNode: isParentBranchNode,
      isLoopingNode: isParentLoopingNode,
    } = getNodeInfo(copyFlow, parentId);
    // Next node is always decendent nodes, not trigger node.
    // So always prefer the [nodes] property
    const newNode: FlowNodeData = getNewNodeData(nextNodeId ?? '', parentId);

    if (!parentNode) {
      // don't know where parent node is.
      return undefined;
    }

    if (isParentTriggerNode) {
      // update all next property of trigger nodes to nextNodeId
      const key = Object.keys(copyFlow?.triggers)?.[0];
      const nextNodeId = copyFlow?.triggers[key].next;
      newNode.next = nextNodeId;
      updateNextIdAllTriggerNodes(copyFlow, newNode.id);
    } else if (isParentBranchNode) {
      // update next property of branch node to newNode.id
      const paths = (parentNode.settings as FlowRouteByIntentSetting)?.paths;
      const index = paths?.findIndex((condition) => condition?.next === nextNodeId);
      if (index === -1) {
        return undefined;
      }
      paths[index].next = newNode.id;
      newNode.next = nextNodeId;
    } else if (isParentLoopingNode) {
      if (directSubNodeLooping) {
        newNode.next = nextNodeId;
        parentNode.settings.nextChildID = newNode.id;
      } else {
        newNode.next = nextNodeId;
        parentNode.next = newNode.id;
      }
    } else {
      // update next property of parent node to newNode.id
      newNode.next = nextNodeId;
      parentNode.next = newNode.id;
    }

    if (nextNodeId && copyFlow?.nodes?.[nextNodeId]) {
      copyFlow.nodes[nextNodeId].prev = newNode.id;
    }

    addNodeToNodesFlow(copyFlow, newNode);
    return { newFlow: copyFlow, newNodeId: newNode.id };
  };

  const addNewPath = (flow: Flow, connectionData: ConnectionData) => {
    // Add new path
    if (!flow) return undefined;
    const copyFlow = newCopy(flow)!;
    const { parentId, nextNodeId } = connectionData;
    const {
      node: parentNode,
      isTriggerNode: isParentTriggerNode,
      isBranchNode: isParentBranchNode,
      isLoopingNode: isParentLoopingNode,
    } = getNodeInfo(copyFlow, parentId);

    if (!parentNode) {
      return undefined;
    }
    let determinedNextNodeId = nextNodeId;
    let firstNode: FlowNodeData | undefined;

    if (!nextNodeId || !getNodeInfo(copyFlow, nextNodeId)?.node) {
      firstNode = getNewNodeData('', '');
      determinedNextNodeId = firstNode.id;
    }

    const newBranchNode: FlowNodeData = getNewBranchNode(determinedNextNodeId ?? '', parentId);
    if (firstNode) {
      firstNode.prev = newBranchNode.id;
    }

    const secondNode = getNewNodeData('', newBranchNode.id);
    (newBranchNode.settings as FlowRouteByIntentSetting).paths[1].next = secondNode.id;

    if (isParentTriggerNode) {
      updateNextIdAllTriggerNodes(copyFlow, newBranchNode.id);
    } else if (isParentBranchNode) {
      const paths = (parentNode.settings as FlowRouteByIntentSetting)?.paths;
      const index = paths?.findIndex((condition) => condition?.next === nextNodeId);
      if (index === -1) {
        return undefined;
      }
      paths[index].next = newBranchNode.id;
    } else if (isParentLoopingNode) {
      if (connectionData.directSubNodeLooping) {
        parentNode.settings.nextChildID = newBranchNode.id;
      }
      if (connectionData.directNextNodeLooping) {
        parentNode.next = newBranchNode.id;
      }
    } else {
      parentNode.next = newBranchNode.id;
    }

    addNodeToNodesFlow(copyFlow, newBranchNode);
    firstNode && addNodeToNodesFlow(copyFlow, firstNode);
    addNodeToNodesFlow(copyFlow, secondNode);
    return copyFlow;
  };

  const addNewSubPath = (flow: Flow, connectionData: ConnectionData) => {
    // Add new subpath
    if (!flow) return undefined;
    const copyFlow = newCopy(flow);
    const { parentId } = connectionData;
    const { node: parentNode } = getNodeInfo(copyFlow, parentId);
    if (!parentNode) {
      return undefined;
    }
    if (parentNode.name !== FlowNodeType.Branch && parentNode.name !== FlowNodeType.Path) {
      return undefined;
    }
    // Add new {next, value} to parentNode.settings.paths.
    const newEmptyNode = getNewNodeData('', parentNode.id);
    const newCondition = {
      id: ulid(),
      settings: {},
      next: newEmptyNode.id,
    };
    (parentNode.settings as FlowRouteByIntentSetting).paths.push(newCondition);
    addNodeToNodesFlow(copyFlow, newEmptyNode);
    return copyFlow;
  };

  const addNewLooping = (flow: Flow, connectionData: ConnectionData) => {
    // Add new looping
    if (!flow) return undefined;
    // let check if we are in looping's next nodes
    const { isLoopingNode: isAncesterLoopingNode, isConnectFromLoopSubNodes } =
      checkAncestorIsLoopingNode(flow, connectionData.parentId);

    if (isAncesterLoopingNode && isConnectFromLoopSubNodes) {
      console.log('Cannot add looping node inside a looping node.');
      // We have 2 case here,
      // 1. we are in looping's sub nodes.
      // 2. we are in looping's next nodes.
      // For case 1, return undefined to terminate.
      return undefined;
    }

    const copyFlow = newCopy(flow);
    const { parentId, nextNodeId } = connectionData;
    const {
      node: parentNode,
      isTriggerNode: isParentTriggerNode,
      isBranchNode: isParentBranchNode,
    } = getNodeInfo(copyFlow, parentId);

    if (!parentNode) {
      return undefined;
    }

    const newLoopingNode = getNewLoopingNode(nextNodeId ?? '', parentId);
    newLoopingNode.displayName = connectionData?.displayName || '';
    newLoopingNode.icon = connectionData?.icon || '';
    newLoopingNode.name = (connectionData?.name as FlowNodeType) || FlowNodeType.Looping;

    if (isParentTriggerNode) {
      // update all next property of trigger nodes to nextNodeId
      updateNextIdAllTriggerNodes(copyFlow, newLoopingNode.id);
    } else if (isParentBranchNode) {
      // update next property of branch node to newNode.id
      const paths = (parentNode.settings as FlowRouteByIntentSetting)?.paths;
      const index = paths?.findIndex((condition) => condition?.next === nextNodeId);
      if (index === -1) {
        return undefined;
      }
      paths[index].next = newLoopingNode.id;
      newLoopingNode.next = nextNodeId;
    } else {
      // update next property of parent node to newNode.id
      parentNode.next = newLoopingNode.id;
    }
    addNodeToNodesFlow(copyFlow, newLoopingNode);
    return copyFlow;
  };

  const duplicateNode = (flow: Flow, nodeId: string) => {
    if (!flow) return undefined;
    const copyFlow = newCopy(flow);
    const { node, isTriggerNode } = getNodeInfo(copyFlow, nodeId);
    if (!node) return undefined;

    const newNode = isTriggerNode
      ? getNewTriggerNodeData('', {
          name: node.name,
          icon: node.icon || '',
          displayName: node.displayName,
        })
      : getNewNodeData('', node.id);

    // Copy common properties
    newNode.name = node.name;
    newNode.icon = node.icon;
    newNode.displayName = node.displayName;
    newNode.settings = newCopy(node.settings);

    if (isTriggerNode) {
      newNode.prev = node.prev;
      copyFlow.triggers[newNode.id] = newNode;
    } else {
      if (node.next && copyFlow.nodes![node.next]) {
        copyFlow.nodes![node.next].prev = newNode.id;
      }
      newNode.next = node.next;
      node.next = newNode.id;
      copyFlow.nodes![newNode.id] = newNode;
    }

    return copyFlow;
  };

  const removeTriggerNode = (flow: Flow, nodeId: string) => {
    // Remove trigger node
    if (!flow?.triggers?.[nodeId]) return undefined;
    if (Object.keys(flow?.triggers).length === 1) return undefined;
    const copyFlow = newCopy(flow);
    delete copyFlow.triggers?.[nodeId];
    return copyFlow;
  };

  const removeNode = (flow: Flow, parentId: string, nodeId: string) => {
    // Remove node
    if (!flow?.nodes?.[nodeId]) return undefined;
    const copyFlow = newCopy(flow);
    // if parent node is branch node, replace the condition item has nodeId by node's setting's next property
    const {
      node: parentNode,
      isBranchNode: isParentBranchNode,
      isTriggerNode: isParentTriggerNode,
      isLoopingNode: isParentLoopingNode,
    } = getNodeInfo(copyFlow, parentId);
    if (!parentNode) {
      return undefined;
    }
    const nextNodeId = copyFlow?.nodes?.[nodeId]?.next ?? '';
    if (isParentTriggerNode) {
      updateNextIdAllTriggerNodes(copyFlow, nextNodeId);
    } else if (isParentBranchNode) {
      const paths = (copyFlow?.nodes?.[parentId]?.settings as FlowRouteByIntentSetting)?.paths;
      const index = paths?.findIndex((condition) => condition?.next === nodeId);
      if (index === -1) {
        return undefined;
      }
      paths[index].next = nextNodeId;
    } else if (isParentLoopingNode) {
      /// if parent.loop === nodeId, mean this node is node inside that looping
      if (parentNode?.settings?.nextChildID === nodeId) {
        parentNode.settings.nextChildID = nextNodeId;
      } else {
        parentNode.next = nextNodeId;
      }
    } else {
      parentNode.next = nextNodeId;
    }

    if (copyFlow?.nodes?.[nextNodeId]) {
      // update parentId of nextNodeId node.
      copyFlow.nodes[nextNodeId].prev = parentNode.id;
    }
    delete copyFlow?.nodes?.[nodeId];
    return copyFlow;
  };

  const removeNodeRecursive = (flow: Flow, nodeId: string) => {
    // if the node id branch node => remove all descendants of this node.
    // else just remove the node.
    if (!flow?.nodes?.[nodeId]) return;

    if (
      flow?.nodes?.[nodeId]?.name !== FlowNodeType.Branch &&
      flow?.nodes?.[nodeId]?.name !== FlowNodeType.Path
    ) {
      if (flow?.nodes?.[nodeId]?.name === FlowNodeType.Looping) {
        removeNodeRecursive(flow, flow?.nodes?.[nodeId]?.settings?.nextChildID);
      }
      if (flow?.nodes?.[nodeId]?.next) {
        removeNodeRecursive(flow, flow?.nodes?.[nodeId]?.next);
      }
      delete flow?.nodes?.[nodeId];
      return;
    }
    const nextIds = (flow?.nodes?.[nodeId]?.settings as FlowRouteByIntentSetting)?.paths?.map(
      (path) => path?.next
    );
    // recusively remove all descendants.
    nextIds?.forEach((nextId) => {
      removeNodeRecursive(flow, nextId);
    });
    delete flow?.nodes?.[nodeId];
  };

  const removePathNode = (flow: Flow, parentId: string, nodeId: string) => {
    // Remove path
    if (!flow?.nodes?.[nodeId]) return undefined;

    const copyFlow = newCopy(flow);

    const {
      node: parentNode,
      isLoopingNode: isParentLoopingNode,
      isTriggerNode: isParentTriggerNode,
      isBranchNode: isParentBranchNode,
    } = getNodeInfo(copyFlow, parentId);

    if (!parentNode) {
      return undefined;
    }

    if (isParentTriggerNode) {
      updateNextIdAllTriggerNodes(copyFlow, '');
    }

    if (isParentLoopingNode) {
      // remove the loop's sub node
      if (parentNode?.settings?.nextChildID === nodeId) {
        parentNode.settings.nextChildID = '';
      }
      // remove the loop's next node
      if (parentNode.next === nodeId) {
        parentNode.next = '';
      }
    }

    if (isParentBranchNode) {
      const paths = (parentNode.settings as FlowRouteByIntentSetting)?.paths;
      const index = paths?.findIndex((condition) => condition?.next === nodeId);
      if (index === -1) {
        return undefined;
      }
      paths[index].next = '';
    }

    // remove all descendants of this node.
    if (
      copyFlow?.nodes?.[nodeId]?.name !== FlowNodeType.Branch &&
      copyFlow?.nodes?.[nodeId]?.name !== FlowNodeType.Path
    ) {
      return undefined;
    }
    removeNodeRecursive(copyFlow, nodeId);

    delete copyFlow.nodes?.[nodeId];
    return copyFlow;
  };

  const removeSubPathNode = (flow: Flow, parentId: string, nodeId: string) => {
    // Remove subpath
    if (!flow?.nodes?.[parentId]) return undefined;
    const copyFlow = newCopy(flow);
    const parentNode = copyFlow?.nodes?.[parentId];
    if (
      !parentNode ||
      (parentNode?.name !== FlowNodeType.Branch && parentNode?.name !== FlowNodeType.Path)
    ) {
      return undefined;
    }

    const paths = (parentNode.settings as FlowRouteByIntentSetting)?.paths;
    const index = paths?.findIndex((condition) => condition?.next === nodeId);
    if (index === -1) {
      return undefined;
    }
    paths?.splice(index, 1);
    removeNodeRecursive(copyFlow, nodeId);
    if (!paths?.length) {
      // remove parent node if no more paths.
      // because the parent is empty branch node
      delete copyFlow?.nodes?.[parentId];
    }
    return copyFlow;
  };

  const removeLoopingNode = (flow: Flow, parentId: string, nodeId: string) => {
    // Remove looping node
    if (!flow || !flow?.nodes?.[nodeId]) return undefined;
    const copyFlow = newCopy(flow);
    const { node: parentNode } = getNodeInfo(copyFlow, parentId);
    if (!parentNode) return undefined;

    if (flow?.nodes?.[nodeId]?.name !== FlowNodeType.Looping) {
      return undefined;
    }

    const loopingNode = copyFlow?.nodes?.[nodeId];
    if (loopingNode?.settings?.nextChildID) {
      removeNodeRecursive(copyFlow, loopingNode?.settings?.nextChildID);
    }
    const newFlow = removeNode(copyFlow, parentId, nodeId);
    return newFlow;
  };

  const removeAllSubNodesLoopingNode = (flow: Flow, nodeId: string) => {
    if (!flow || !flow?.nodes?.[nodeId]) return undefined;
    const copyFlow = newCopy(flow);
    const loopingNode = copyFlow?.nodes?.[nodeId];
    if (loopingNode?.settings?.nextChildID) {
      removeNodeRecursive(copyFlow, loopingNode?.settings?.nextChildID);
    }
    return copyFlow;
  };

  const replaceNodeType = (
    flow: Flow,
    nodeId: string,
    config: { name: FlowNodeType; icon?: string; displayName?: string }
  ) => {
    // replace node name
    if (!flow || (!flow?.nodes?.[nodeId] && !flow?.triggers?.[nodeId])) return undefined;
    const itemObject = flow?.nodes?.[nodeId] ? 'nodes' : 'triggers';
    const copyFlow = newCopy(flow);
    copyFlow![itemObject]![nodeId].name = config.name;
    copyFlow![itemObject]![nodeId].icon = config?.icon || '';
    copyFlow![itemObject]![nodeId].displayName = config?.displayName || '';
    copyFlow![itemObject]![nodeId].settings = {} as FlowNodeSetting;

    return copyFlow;
  };

  return {
    getNodeInfo,
    getNewNodeData,
    getNewBranchNode,
    getPreviousNodes,
    getNewLoopingNode,
    getNewTriggerNodeData,
    addNewNode,
    addNewPath,
    addNewTrigger,
    addNewSubPath,
    addNewLooping,
    duplicateNode,
    removeNode,
    removePathNode,
    replaceNodeType,
    removeTriggerNode,
    removeSubPathNode,
    removeLoopingNode,
    removeAllSubNodesLoopingNode,
    checkAncestorIsLoopingNode,
  };
}

export default useFlowActions;
