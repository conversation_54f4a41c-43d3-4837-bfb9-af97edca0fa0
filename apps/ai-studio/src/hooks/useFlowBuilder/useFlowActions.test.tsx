import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import useFlowActions from './useFlowActions';
import {
  type Flow,
  type FlowNodeData,
  FlowNodeType,
  type FlowRouteByIntentSetting,
} from '@/models/flow';

// Mock ulid to return predictable IDs for testing
vi.mock('ulid', () => ({
  ulid: () => 'test-id',
}));

describe('useFlowActions', () => {
  let mockFlow: Flow;

  beforeEach(() => {
    // Reset the mock flow before each test
    mockFlow = {
      id: 'flow-1',
      name: 'Test Flow',
      description: 'Test flow description',
      status: 'enabled',
      triggers: {
        'trigger-1': {
          id: 'trigger-1',
          name: undefined,
          displayName: 'Trigger 1',
          description: 'Test trigger',
          settings: {},
          next: 'node-1',
        } as unknown as FlowNodeData,
      },
      nodes: {
        'node-1': {
          id: 'node-1',
          name: FlowNodeType.EmptyNode,
          displayName: 'Node 1',
          description: 'Test node',
          settings: {},
          prev: 'trigger-1',
          next: 'node-2',
        } as unknown as FlowNodeData,
        'node-2': {
          id: 'node-2',
          name: FlowNodeType.EmptyNode,
          displayName: 'Node 2',
          description: 'Test node 2',
          settings: {},
          next: '',
          prev: 'node-1',
        } as unknown as FlowNodeData,
        'branch-1': {
          id: 'branch-1',
          name: FlowNodeType.Path,
          displayName: 'Branch 1',
          description: 'Test branch',
          settings: {
            paths: [
              {
                id: 'condition-1',
                name: 'condition 1',
                settings: {},
                next: 'node-3',
              },
              {
                id: 'condition-2',
                name: 'condition 2',
                settings: {},
                next: 'node-4',
              },
            ],
          },
          prev: 'node-2',
        },
        'node-3': {
          id: 'node-3',
          name: FlowNodeType.EmptyNode,
          displayName: 'Node 3',
          description: 'Test node 3',
          settings: {},
          next: '',
          prev: 'branch-1',
        } as unknown as FlowNodeData,
        'node-4': {
          id: 'node-4',
          name: FlowNodeType.EmptyNode,
          displayName: 'Node 4',
          description: 'Test node 4',
          settings: {},
          next: '',
          prev: 'branch-1',
        } as unknown as FlowNodeData,
        'looping-1': {
          id: 'looping-1',
          name: FlowNodeType.Looping,
          displayName: 'Looping 1',
          description: 'Test looping',
          settings: {},
          loop: 'node-5',
          next: 'node-6',
          prev: 'node-2',
        } as unknown as FlowNodeData,
        'node-5': {
          id: 'node-5',
          name: FlowNodeType.EmptyNode,
          displayName: 'Node 5',
          description: 'Test node 5',
          settings: {},
          next: '',
          prev: 'looping-1',
        } as unknown as FlowNodeData,
        'node-6': {
          id: 'node-6',
          name: FlowNodeType.EmptyNode,
          displayName: 'Node 6',
          description: 'Test node 6',
          settings: {},
          next: '',
          prev: 'looping-1',
        } as unknown as FlowNodeData,
        'nested-looping-parent': {
          id: 'nested-looping-parent',
          name: FlowNodeType.EmptyNode,
          displayName: 'Nested Looping Parent',
          description: 'Parent of a nested looping node',
          settings: {},
          next: 'nested-looping',
          prev: 'trigger-1',
        } as unknown as FlowNodeData,
        'nested-looping': {
          id: 'nested-looping',
          name: FlowNodeType.Looping,
          displayName: 'Nested Looping',
          description: 'Nested looping node',
          settings: {},
          loop: 'nested-node',
          next: '',
          prev: 'nested-looping-parent',
        } as unknown as FlowNodeData,
        'nested-node': {
          id: 'nested-node',
          name: FlowNodeType.EmptyNode,
          displayName: 'Nested Node',
          description: 'Node inside nested looping',
          settings: {},
          next: '',
          prev: 'nested-looping',
        } as unknown as FlowNodeData,
      },
    };
  });

  it('should add a new trigger', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.addNewTrigger(mockFlow, {
      name: FlowNodeType.NewTrigger,
      displayName: 'Test Trigger',
      icon: 'trigger',
    });

    expect(updatedFlow).toBeDefined();
    expect(Object.keys(updatedFlow!.triggers)).toHaveLength(2);
    expect(updatedFlow!.triggers['test-id']).toBeDefined();
    expect(updatedFlow!.triggers['test-id'].next).toBe('node-1');
  });

  it('should return undefined when adding a trigger to an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.addNewTrigger(undefined as unknown as Flow, {
      name: FlowNodeType.NewTrigger,
      displayName: 'Test Trigger',
      icon: 'trigger',
    });

    expect(updatedFlow).toBeUndefined();
  });

  it('should add a new node after a trigger node', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'trigger-1',
      nextNodeId: 'node-1',
    };

    const response = result.current.addNewNode(mockFlow, connectionData);

    expect(response).toBeDefined();
    if (response?.newFlow) {
      expect(response.newFlow.nodes!['test-id']).toBeDefined();
      expect(response!.newFlow.triggers['trigger-1'].next).toBe('test-id');
      expect(response!.newFlow.nodes!['test-id'].next).toBe('node-1');
    }
  });

  it('should add a new node after a branch node condition', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'branch-1',
      nextNodeId: 'node-3',
    };

    const response = result.current.addNewNode(mockFlow, connectionData);

    expect(response).toBeDefined();
    if (response?.newFlow) {
      expect(response.newFlow.nodes!['test-id']).toBeDefined();
      expect(
        (response.newFlow.nodes!['branch-1'].settings as FlowRouteByIntentSetting).paths[0].next
      ).toBe('test-id');
      expect(response.newFlow.nodes!['test-id'].next).toBe('node-3');
    }
  });

  it('should add a new node after a regular node', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'node-1',
      nextNodeId: 'node-2',
    };

    const response = result.current.addNewNode(mockFlow, connectionData);

    expect(response).toBeDefined();
    if (response?.newFlow) {
      expect(response.newFlow.nodes!['node-1'].next).toBe('test-id');
      expect(response.newFlow.nodes!['test-id']).toBeDefined();
      expect(response.newFlow.nodes!['test-id'].next).toBe('node-2');
    }
  });

  it('should return undefined when adding a node to an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'trigger-1',
      nextNodeId: 'node-1',
    };

    const response = result.current.addNewNode(undefined as unknown as Flow, connectionData);

    expect(response).toBeUndefined();
  });

  it('should return undefined when parent node is not found', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'non-existent-id',
      nextNodeId: 'node-1',
    };

    const response = result.current.addNewNode(mockFlow, connectionData);

    expect(response).toBeUndefined();
  });

  it('should return undefined when branch node condition is not found', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'branch-1',
      nextNodeId: 'non-existent-id',
    };

    const response = result.current.addNewNode(mockFlow, connectionData);

    expect(response).toBeUndefined();
  });

  it('should add a new path after a trigger node', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'trigger-1',
      nextNodeId: 'node-1',
    };

    const updatedFlow = result.current.addNewPath(mockFlow, connectionData);

    expect(updatedFlow).toBeDefined();
    // Check that a new node was added
    expect(updatedFlow!.nodes!['test-id']).toBeDefined();
    // Check that the trigger now points to the new node
    expect(updatedFlow!.triggers['trigger-1'].next).toBe('test-id');
    // Check that the new node has paths with the correct next values
    if ((updatedFlow!.nodes!['test-id'].settings as FlowRouteByIntentSetting).paths) {
      const paths = (updatedFlow!.nodes!['test-id'].settings as FlowRouteByIntentSetting).paths;
      expect(paths[0].next).toBeDefined();
      expect(paths[1].next).toBeDefined();
    }
  });

  it('should add a new path with a new node when nextNodeId is not provided', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'trigger-1',
      nextNodeId: '',
    };

    const updatedFlow = result.current.addNewPath(mockFlow, connectionData);

    expect(updatedFlow).toBeDefined();
    // Check that a new node was added
    expect(updatedFlow!.nodes!['test-id']).toBeDefined(); // Branch node
    // Check that at least one new node was added
    expect(Object.keys(updatedFlow!.nodes!).length).toBeGreaterThan(
      Object.keys(mockFlow.nodes!).length
    );
  });

  it('should add a new path after a branch node condition', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'branch-1',
      nextNodeId: 'node-3',
    };

    const updatedFlow = result.current.addNewPath(mockFlow, connectionData);

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['test-id']).toBeDefined();
    expect(
      (updatedFlow!.nodes!['branch-1'].settings as FlowRouteByIntentSetting).paths[0].next
    ).toBe('test-id');
  });

  it('should add a new path after a regular node', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'node-1',
      nextNodeId: 'node-2',
    };

    const updatedFlow = result.current.addNewPath(mockFlow, connectionData);

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['test-id']).toBeDefined();
    expect(updatedFlow!.nodes!['node-1'].next).toBe('test-id');
  });

  it('should return undefined when adding a path to an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'trigger-1',
      nextNodeId: 'node-1',
    };

    const updatedFlow = result.current.addNewPath(undefined as unknown as Flow, connectionData);

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when parent node is not found for path', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'non-existent-id',
      nextNodeId: 'node-1',
    };

    const updatedFlow = result.current.addNewPath(mockFlow, connectionData);

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when branch node condition is not found for path', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'branch-1',
      nextNodeId: 'non-existent-id',
    };

    const updatedFlow = result.current.addNewPath(mockFlow, connectionData);

    expect(updatedFlow).toBeUndefined();
  });

  it('should add a new subpath to a branch node', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'branch-1',
      nextNodeId: '',
    };

    const updatedFlow = result.current.addNewSubPath(mockFlow, connectionData);

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['test-id']).toBeDefined();
    expect(
      (updatedFlow!.nodes!['branch-1'].settings as FlowRouteByIntentSetting).paths
    ).toHaveLength(3);
    expect(
      (updatedFlow!.nodes!['branch-1'].settings as FlowRouteByIntentSetting).paths[2].next
    ).toBe('test-id');
  });

  it('should return undefined when adding a subpath to an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'branch-1',
      nextNodeId: '',
    };

    const updatedFlow = result.current.addNewSubPath(undefined as unknown as Flow, connectionData);

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when parent node is not found for subpath', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'non-existent-id',
      nextNodeId: '',
    };

    const updatedFlow = result.current.addNewSubPath(mockFlow, connectionData);

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when parent node is not a branch node for subpath', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'node-1',
      nextNodeId: '',
    };

    const updatedFlow = result.current.addNewSubPath(mockFlow, connectionData);

    expect(updatedFlow).toBeUndefined();
  });

  it('should remove a trigger node', () => {
    // Add another trigger to allow removal
    mockFlow.triggers['trigger-2'] = {
      id: 'trigger-2',
      type: undefined,
      displayName: 'Trigger 2',
      description: 'Test trigger 2',
      settings: {},
      next: 'node-1',
    } as unknown as FlowNodeData;

    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeTriggerNode(mockFlow, 'trigger-1');

    expect(updatedFlow).toBeDefined();
    expect(Object.keys(updatedFlow!.triggers)).toHaveLength(1);
    expect(updatedFlow!.triggers['trigger-1']).toBeUndefined();
    expect(updatedFlow!.triggers['trigger-2']).toBeDefined();
  });

  it('should return undefined when removing the only trigger node', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeTriggerNode(mockFlow, 'trigger-1');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when removing a non-existent trigger node', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeTriggerNode(mockFlow, 'non-existent-id');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when removing a trigger from an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeTriggerNode(undefined as unknown as Flow, 'trigger-1');

    expect(updatedFlow).toBeUndefined();
  });

  it('should remove a node after a trigger node', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeNode(mockFlow, 'trigger-1', 'node-1');

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['node-1']).toBeUndefined();
    expect(updatedFlow!.triggers['trigger-1'].next).toBe('node-2');
  });

  it('should remove a node after a branch node condition', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeNode(mockFlow, 'branch-1', 'node-3');

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['node-3']).toBeUndefined();
    expect(
      (updatedFlow!.nodes!['branch-1'].settings as FlowRouteByIntentSetting).paths[0].next
    ).toBe('');
  });

  it('should remove a node after a regular node', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeNode(mockFlow, 'node-1', 'node-2');

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['node-2']).toBeUndefined();
    expect(updatedFlow!.nodes!['node-1'].next).toBe('');
  });

  it('should return undefined when removing a node from an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeNode(undefined as unknown as Flow, 'node-1', 'node-2');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when removing a non-existent node', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeNode(mockFlow, 'node-1', 'non-existent-id');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when parent node is not found for node removal', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeNode(mockFlow, 'non-existent-id', 'node-1');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when branch node condition is not found for node removal', () => {
    // Create a branch node with no matching condition
    mockFlow.nodes!['branch-2'] = {
      id: 'branch-2',
      name: FlowNodeType.Path,
      displayName: 'Branch 2',
      description: 'Test branch 2',
      settings: {
        paths: [
          {
            id: 'condition-1',
            name: 'condition 1',
            settings: {},
            next: 'other-node',
          },
        ],
      },
      prev: '',
    };

    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeNode(mockFlow, 'branch-2', 'node-3');

    expect(updatedFlow).toBeUndefined();
  });

  it('should remove a path node (branch node)', () => {
    const { result } = renderHook(() => useFlowActions());
    // Create a modified mockFlow with a branch node that has a parent
    const modifiedFlow = JSON.parse(JSON.stringify(mockFlow));
    modifiedFlow.nodes['branch-1'].parent = 'node-1';

    const updatedFlow = result.current.removePathNode(modifiedFlow, 'node-1', 'branch-1');

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['branch-1']).toBeUndefined();
    expect(updatedFlow!.nodes!['node-3']).toBeUndefined();
    expect(updatedFlow!.nodes!['node-4']).toBeUndefined();
  });

  it('should return undefined when removing a path from an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removePathNode(
      undefined as unknown as Flow,
      'node-1',
      'branch-1'
    );

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when removing a non-existent path node', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removePathNode(mockFlow, 'node-1', 'non-existent-id');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when removing a non-branch node as a path', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removePathNode(mockFlow, 'node-1', 'node-2');

    expect(updatedFlow).toBeUndefined();
  });

  it('should remove a subpath node', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeSubPathNode(mockFlow, 'branch-1', 'node-3');

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['node-3']).toBeUndefined();
    expect(
      (updatedFlow!.nodes!['branch-1'].settings as FlowRouteByIntentSetting).paths
    ).toHaveLength(1);
  });

  it('should remove the branch node when removing the last subpath', () => {
    // Create a branch node with only one condition
    mockFlow.nodes!['branch-2'] = {
      id: 'branch-2',
      name: FlowNodeType.Path,
      displayName: 'Branch 2',
      description: 'Test branch 2',
      settings: {
        paths: [
          {
            id: 'condition-1',
            name: 'condition 1',
            settings: {},
            next: 'node-5',
          },
        ],
      },
      prev: '',
    };
    mockFlow.nodes!['node-5'] = {
      id: 'node-5',
      name: FlowNodeType.EmptyNode,
      displayName: 'Node 5',
      description: 'Test node 5',
      settings: {
        next: '',
      },
      prev: 'branch-2',
    } as unknown as FlowNodeData;

    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeSubPathNode(mockFlow, 'branch-2', 'node-5');

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['node-5']).toBeUndefined();
    expect(updatedFlow!.nodes!['branch-2']).toBeUndefined();
  });

  it('should return undefined when removing a subpath from an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeSubPathNode(
      undefined as unknown as Flow,
      'branch-1',
      'node-3'
    );

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when parent node is not found for subpath removal', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeSubPathNode(mockFlow, 'non-existent-id', 'node-3');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when parent node is not a branch node for subpath removal', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeSubPathNode(mockFlow, 'node-1', 'node-2');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when subpath node is not found in branch paths', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeSubPathNode(mockFlow, 'branch-1', 'non-existent-id');

    expect(updatedFlow).toBeUndefined();
  });

  // Tests for looping node functionality
  it('should add a new node after a looping node (direct sub-node)', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'looping-1',
      nextNodeId: 'node-5',
      directSubNodeLooping: true,
    };

    const response = result.current.addNewNode(mockFlow, connectionData);

    expect(response).toBeDefined();
    if (response?.newFlow) {
      expect(response.newFlow.nodes!['test-id']).toBeDefined();
      expect(response.newFlow.nodes!['looping-1'].settings?.nextChildID).toBe('test-id');
      expect(response.newFlow.nodes!['test-id'].next).toBe('node-5');
    }
  });

  it('should add a new node after a looping node (next node)', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'looping-1',
      nextNodeId: 'node-6',
      directSubNodeLooping: false,
    };

    const response = result.current.addNewNode(mockFlow, connectionData);

    expect(response).toBeDefined();
    if (response?.newFlow) {
      expect(response.newFlow.nodes!['test-id']).toBeDefined();
      expect(response.newFlow.nodes!['looping-1'].next).toBe('test-id');
      expect(response.newFlow.nodes!['test-id'].next).toBe('node-6');
    }
  });

  it('should add a new path after a looping node', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'looping-1',
      nextNodeId: 'node-5',
      directSubNodeLooping: true,
    };

    const updatedFlow = result.current.addNewPath(mockFlow, connectionData);

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['test-id']).toBeDefined();
    expect(updatedFlow!.nodes!['looping-1']?.settings?.nextChildID).toBe('test-id');
  });

  it('should add a new looping node', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'node-1',
      nextNodeId: 'node-2',
    };

    const updatedFlow = result.current.addNewLooping(mockFlow, connectionData);

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['test-id']).toBeDefined();
    expect(updatedFlow!.nodes!['test-id'].name).toBe(FlowNodeType.Looping);
    expect(updatedFlow!.nodes!['node-1'].next).toBe('test-id');
  });

  it('should add a new looping node after a branch node condition', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'branch-1',
      nextNodeId: 'node-3',
    };

    const updatedFlow = result.current.addNewLooping(mockFlow, connectionData);

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['test-id']).toBeDefined();
    expect(updatedFlow!.nodes!['test-id'].name).toBe(FlowNodeType.Looping);
    expect(
      (updatedFlow!.nodes!['branch-1'].settings as FlowRouteByIntentSetting).paths[0].next
    ).toBe('test-id');
  });

  it('should add a new looping node after a trigger node', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'trigger-1',
      nextNodeId: 'node-1',
    };

    const updatedFlow = result.current.addNewLooping(mockFlow, connectionData);

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['test-id']).toBeDefined();
    expect(updatedFlow!.nodes!['test-id'].name).toBe(FlowNodeType.Looping);
    expect(updatedFlow!.triggers['trigger-1'].next).toBe('test-id');
  });

  it('should return undefined when adding a looping node to an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'node-1',
      nextNodeId: 'node-2',
    };

    const updatedFlow = result.current.addNewLooping(undefined as unknown as Flow, connectionData);

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when adding a looping node to a parent that is not found', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'non-existent-id',
      nextNodeId: 'node-2',
    };

    const updatedFlow = result.current.addNewLooping(mockFlow, connectionData);

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when adding a looping node to a parent that is already a looping node', () => {
    const { result } = renderHook(() => useFlowActions());
    // Create a modified mockFlow with a node that is a child of a looping node
    const modifiedFlow = JSON.parse(JSON.stringify(mockFlow));
    modifiedFlow.nodes['node-5'].prev = 'looping-1';
    modifiedFlow.nodes['looping-1'].settings = { nextChildID: 'node-5' };

    const connectionData = {
      parentId: 'node-5',
      nextNodeId: '',
    };

    // The implementation should return undefined because we can't add a looping node
    // inside another looping node
    const updatedFlow = result.current.addNewLooping(modifiedFlow, connectionData);

    // Verify that it returns undefined as expected
    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when adding a looping node to a parent that has a looping ancestor', () => {
    const { result } = renderHook(() => useFlowActions());
    // Create a modified mockFlow with proper looping structure
    const modifiedFlow = JSON.parse(JSON.stringify(mockFlow));
    modifiedFlow.nodes['nested-node'].prev = 'nested-looping';
    modifiedFlow.nodes['nested-looping'].settings = { nextChildID: 'nested-node' };

    const connectionData = {
      parentId: 'nested-node',
      nextNodeId: '',
    };

    // The implementation should return undefined because we can't add a looping node
    // inside another looping node (even if it's an ancestor)
    const updatedFlow = result.current.addNewLooping(modifiedFlow, connectionData);

    // Verify that it returns undefined as expected
    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when branch node condition is not found for looping node', () => {
    const { result } = renderHook(() => useFlowActions());
    const connectionData = {
      parentId: 'branch-1',
      nextNodeId: 'non-existent-id',
    };

    const updatedFlow = result.current.addNewLooping(mockFlow, connectionData);

    expect(updatedFlow).toBeUndefined();
  });

  it('should remove a node after a looping node (loop node)', () => {
    const { result } = renderHook(() => useFlowActions());
    // Create a modified mockFlow with proper looping structure
    const modifiedFlow = JSON.parse(JSON.stringify(mockFlow));
    modifiedFlow.nodes['looping-1'].settings = { nextChildID: 'node-5' };

    const updatedFlow = result.current.removeNode(modifiedFlow, 'looping-1', 'node-5');

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['node-5']).toBeUndefined();
    expect(updatedFlow!.nodes!['looping-1'].settings?.nextChildID).toBe('');
  });

  it('should remove a node after a looping node (next node)', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeNode(mockFlow, 'looping-1', 'node-6');

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['node-6']).toBeUndefined();
    expect(updatedFlow!.nodes!['looping-1'].next).toBe('');
  });

  it('should remove a looping node', () => {
    // Create a simpler flow with a looping node
    const simpleFlow: Flow = {
      id: 'simple-flow',
      name: 'Simple Flow',
      description: 'Test flow with a looping node',
      status: 'enabled',
      triggers: {
        'trigger-1': {
          id: 'trigger-1',
          name: undefined,
          displayName: 'Trigger 1',
          description: 'Test trigger',
          settings: {},
          next: 'parent-node',
        } as unknown as FlowNodeData,
      },
      nodes: {
        'parent-node': {
          id: 'parent-node',
          name: FlowNodeType.EmptyNode,
          displayName: 'Parent Node',
          description: 'Parent of looping node',
          settings: {},
          prev: 'trigger-1',
          next: 'looping-node',
        } as unknown as FlowNodeData,
        'looping-node': {
          id: 'looping-node',
          name: FlowNodeType.Looping,
          displayName: 'Looping Node',
          description: 'Test looping node',
          settings: {
            nextChildID: 'loop-child',
          },
          next: '',
          prev: 'parent-node',
        } as unknown as FlowNodeData,
        'loop-child': {
          id: 'loop-child',
          name: FlowNodeType.EmptyNode,
          displayName: 'Loop Child',
          description: 'Child of looping node',
          settings: {},
          next: '',
          prev: 'looping-node',
        } as unknown as FlowNodeData,
      },
    };

    const { result } = renderHook(() => useFlowActions());

    // Call removeLoopingNode with our test flow
    const updatedFlow = result.current.removeLoopingNode(simpleFlow, 'parent-node', 'looping-node');

    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['looping-node']).toBeUndefined();
    // We can't test for loop-child being removed since removeNodeRecursive is private
    // and we can't mock it in this test
  });

  it('should return undefined when removing a looping node from an undefined flow', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeLoopingNode(
      undefined as unknown as Flow,
      'node-2',
      'looping-1'
    );

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when removing a non-existent looping node', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeLoopingNode(mockFlow, 'node-2', 'non-existent-id');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when parent node is not found for looping node removal', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeLoopingNode(mockFlow, 'non-existent-id', 'looping-1');

    expect(updatedFlow).toBeUndefined();
  });

  it('should return undefined when removing a non-looping node as a looping node', () => {
    const { result } = renderHook(() => useFlowActions());
    const updatedFlow = result.current.removeLoopingNode(mockFlow, 'node-1', 'node-2');

    expect(updatedFlow).toBeUndefined();
  });

  it('should test removeNodeRecursive indirectly', () => {
    const { result } = renderHook(() => useFlowActions());

    // Create a simpler test for removeNodeRecursive by testing its effects indirectly
    // We'll test that when we remove a node, its next reference is cleared
    const updatedFlow = result.current.removeNode(mockFlow, 'node-1', 'node-2');

    // Verify that the node was removed and its parent's next reference is cleared
    expect(updatedFlow).toBeDefined();
    expect(updatedFlow!.nodes!['node-2']).toBeUndefined();
    expect(updatedFlow!.nodes!['node-1'].next).toBe('');
  });

  // Tests for schedule node functionality (TK-8980)
  describe('Schedule Node Functionality', () => {
    it('should add a new schedule trigger node', () => {
      const { result } = renderHook(() => useFlowActions());

      const initData = {
        name: FlowNodeType.Schedule,
        displayName: 'Schedule Trigger',
        icon: '📅',
      };

      const updatedFlow = result.current.addNewTrigger(mockFlow, initData);

      expect(updatedFlow).toBeDefined();
      expect(updatedFlow!.triggers).toBeDefined();

      // Find the new schedule trigger
      const scheduleTrigger = Object.values(updatedFlow!.triggers).find(
        (trigger) => trigger.name === FlowNodeType.Schedule
      );

      expect(scheduleTrigger).toBeDefined();
      expect(scheduleTrigger!.displayName).toBe('Schedule Trigger');
      expect(scheduleTrigger!.icon).toBe('📅');
    });

    it('should add a new schedule trigger node with ScheduleTrigger type', () => {
      const { result } = renderHook(() => useFlowActions());

      const initData = {
        name: FlowNodeType.ScheduleTrigger,
        displayName: 'Schedule Trigger',
        icon: '📅',
      };

      const updatedFlow = result.current.addNewTrigger(mockFlow, initData);

      expect(updatedFlow).toBeDefined();
      expect(updatedFlow!.triggers).toBeDefined();

      // Find the new schedule trigger
      const scheduleTrigger = Object.values(updatedFlow!.triggers).find(
        (trigger) => trigger.name === FlowNodeType.ScheduleTrigger
      );

      expect(scheduleTrigger).toBeDefined();
      expect(scheduleTrigger!.displayName).toBe('Schedule Trigger');
      expect(scheduleTrigger!.icon).toBe('📅');
    });

    it('should update next property of all trigger nodes when adding schedule trigger', () => {
      const { result } = renderHook(() => useFlowActions());

      const initData = {
        name: FlowNodeType.Schedule,
        displayName: 'Schedule Trigger',
        icon: '📅',
      };

      const updatedFlow = result.current.addNewTrigger(mockFlow, initData);

      expect(updatedFlow).toBeDefined();

      // All triggers should have the same next node ID
      const triggerValues = Object.values(updatedFlow!.triggers);
      expect(triggerValues.length).toBeGreaterThan(1);

      const firstTriggerNext = triggerValues[0].next;
      triggerValues.forEach((trigger) => {
        expect(trigger.next).toBe(firstTriggerNext);
      });
    });

    it('should handle schedule node in getNewTriggerNodeData', () => {
      const { result } = renderHook(() => useFlowActions());

      const initData = {
        name: FlowNodeType.Schedule,
        displayName: 'Schedule Trigger',
        icon: '📅',
      };

      // Test the internal function by adding a trigger and checking the result
      const updatedFlow = result.current.addNewTrigger(mockFlow, initData);

      expect(updatedFlow).toBeDefined();
      const scheduleTrigger = Object.values(updatedFlow!.triggers).find(
        (trigger) => trigger.name === FlowNodeType.Schedule
      );

      expect(scheduleTrigger).toBeDefined();
      expect(scheduleTrigger!.id).toBe('test-id'); // From mocked ulid
      expect(scheduleTrigger!.name).toBe(FlowNodeType.Schedule);
      expect(scheduleTrigger!.displayName).toBe('Schedule Trigger');
      expect(scheduleTrigger!.icon).toBe('📅');
      expect(scheduleTrigger!.settings).toEqual({});
    });

    it('should handle schedule node removal correctly', () => {
      const { result } = renderHook(() => useFlowActions());

      // First add a schedule trigger
      const initData = {
        name: FlowNodeType.Schedule,
        displayName: 'Schedule Trigger',
        icon: '📅',
      };

      const flowWithSchedule = result.current.addNewTrigger(mockFlow, initData);
      expect(flowWithSchedule).toBeDefined();

      // Find the schedule trigger ID
      const scheduleTrigger = Object.values(flowWithSchedule!.triggers).find(
        (trigger) => trigger.name === FlowNodeType.Schedule
      );
      expect(scheduleTrigger).toBeDefined();

      // Remove the schedule trigger
      const updatedFlow = result.current.removeTriggerNode(flowWithSchedule!, scheduleTrigger!.id);

      expect(updatedFlow).toBeDefined();
      expect(updatedFlow!.triggers[scheduleTrigger!.id]).toBeUndefined();
    });
  });

  // Tests for getPreviousNodes function with trigger node handling
  describe('getPreviousNodes with trigger node handling', () => {
    it('should include all triggers when reaching a trigger node in getPreviousNodes', () => {
      const { result } = renderHook(() => useFlowActions());

      // Create a flow where a node connects back to a trigger
      const testFlow: Flow = {
        id: 'test-flow',
        name: 'Test Flow',
        description: 'Test flow for getPreviousNodes',
        status: 'enabled',
        triggers: {
          'trigger-1': {
            id: 'trigger-1',
            name: FlowNodeType.NewTrigger,
            displayName: 'Trigger 1',
            description: 'Test trigger 1',
            settings: {},
            next: 'node-1',
          } as unknown as FlowNodeData,
          'trigger-2': {
            id: 'trigger-2',
            name: FlowNodeType.Schedule,
            displayName: 'Trigger 2',
            description: 'Test trigger 2',
            settings: {},
            next: 'node-1',
          } as unknown as FlowNodeData,
        },
        nodes: {
          'node-1': {
            id: 'node-1',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 1',
            description: 'Test node 1',
            settings: {},
            prev: 'trigger-1',
            next: 'node-2',
          } as unknown as FlowNodeData,
          'node-2': {
            id: 'node-2',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 2',
            description: 'Test node 2',
            settings: {},
            prev: 'node-1',
            next: 'trigger-1', // This creates a connection back to trigger
          } as unknown as FlowNodeData,
        },
      };

      // Test by adding a new node after node-2, which should trigger the getPreviousNodes logic
      const connectionData = {
        parentId: 'node-2',
        nextNodeId: 'trigger-1',
      };

      const response = result.current.addNewNode(testFlow, connectionData);

      expect(response).toBeDefined();
      if (response?.newFlow) {
        // The new node should be inserted between node-2 and trigger-1
        expect(response.newFlow.nodes!['test-id']).toBeDefined();
        expect(response.newFlow.nodes!['node-2'].next).toBe('test-id');
        expect(response.newFlow.nodes!['test-id'].next).toBe('trigger-1');
      }
    });

    it('should handle getPreviousNodes when node has prev pointing to trigger', () => {
      const { result } = renderHook(() => useFlowActions());

      // Create a flow where a node's prev points directly to a trigger
      const testFlow: Flow = {
        id: 'test-flow',
        name: 'Test Flow',
        description: 'Test flow for getPreviousNodes',
        status: 'enabled',
        triggers: {
          'trigger-1': {
            id: 'trigger-1',
            name: FlowNodeType.NewTrigger,
            displayName: 'Trigger 1',
            description: 'Test trigger 1',
            settings: {},
            next: 'node-1',
          } as unknown as FlowNodeData,
          'trigger-2': {
            id: 'trigger-2',
            name: FlowNodeType.Schedule,
            displayName: 'Trigger 2',
            description: 'Test trigger 2',
            settings: {},
            next: 'node-1',
          } as unknown as FlowNodeData,
        },
        nodes: {
          'node-1': {
            id: 'node-1',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 1',
            description: 'Test node 1',
            settings: {},
            prev: 'trigger-1', // Direct connection to trigger
            next: '',
          } as unknown as FlowNodeData,
        },
      };

      // Test by adding a new node after node-1
      const connectionData = {
        parentId: 'node-1',
        nextNodeId: '',
      };

      const response = result.current.addNewNode(testFlow, connectionData);

      expect(response).toBeDefined();
      if (response?.newFlow) {
        // The new node should be added successfully
        expect(response.newFlow.nodes!['test-id']).toBeDefined();
        expect(response.newFlow.nodes!['node-1'].next).toBe('test-id');
      }
    });

    it('should handle empty triggers object in getPreviousNodes', () => {
      const { result } = renderHook(() => useFlowActions());

      // Create a flow with empty triggers
      const testFlow: Flow = {
        id: 'test-flow',
        name: 'Test Flow',
        description: 'Test flow with empty triggers',
        status: 'enabled',
        triggers: {},
        nodes: {
          'node-1': {
            id: 'node-1',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 1',
            description: 'Test node 1',
            settings: {},
            prev: '',
            next: 'node-2',
          } as unknown as FlowNodeData,
          'node-2': {
            id: 'node-2',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 2',
            description: 'Test node 2',
            settings: {},
            prev: 'node-1',
            next: '',
          } as unknown as FlowNodeData,
        },
      };

      // Test by adding a new node
      const connectionData = {
        parentId: 'node-1',
        nextNodeId: 'node-2',
      };

      const response = result.current.addNewNode(testFlow, connectionData);

      expect(response).toBeDefined();
      if (response?.newFlow) {
        // The new node should be added successfully even with empty triggers
        expect(response.newFlow.nodes!['test-id']).toBeDefined();
        expect(response.newFlow.nodes!['node-1'].next).toBe('test-id');
        expect(response.newFlow.nodes!['test-id'].next).toBe('node-2');
      }
    });

    it('should push all triggers when reaching trigger node in chain', () => {
      const { result } = renderHook(() => useFlowActions());

      // Create a flow where a node chain leads back to a trigger
      const testFlow: Flow = {
        id: 'test-flow',
        name: 'Test Flow',
        description: 'Test flow with trigger in chain',
        status: 'enabled',
        triggers: {
          'trigger-1': {
            id: 'trigger-1',
            name: FlowNodeType.NewTrigger,
            displayName: 'Trigger 1',
            description: 'Test trigger 1',
            settings: {},
            next: 'node-1',
          } as unknown as FlowNodeData,
          'trigger-2': {
            id: 'trigger-2',
            name: FlowNodeType.Schedule,
            displayName: 'Trigger 2',
            description: 'Test trigger 2',
            settings: {},
            next: 'node-1',
          } as unknown as FlowNodeData,
        },
        nodes: {
          'node-1': {
            id: 'node-1',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 1',
            description: 'Test node 1',
            settings: {},
            prev: 'trigger-1',
            next: 'node-2',
          } as unknown as FlowNodeData,
          'node-2': {
            id: 'node-2',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 2',
            description: 'Test node 2',
            settings: {},
            prev: 'node-1',
            next: 'node-3',
          } as unknown as FlowNodeData,
          'node-3': {
            id: 'node-3',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 3',
            description: 'Test node 3',
            settings: {},
            prev: 'node-2',
            next: 'trigger-1', // This creates a loop back to trigger
          } as unknown as FlowNodeData,
        },
      };

      // Test by adding a new node after node-3, which should trigger the getPreviousNodes logic
      // that encounters a trigger node in the chain
      const connectionData = {
        parentId: 'node-3',
        nextNodeId: 'trigger-1',
      };

      const response = result.current.addNewNode(testFlow, connectionData);

      expect(response).toBeDefined();
      if (response?.newFlow) {
        // The new node should be inserted successfully
        expect(response.newFlow.nodes!['test-id']).toBeDefined();
        expect(response.newFlow.nodes!['node-3'].next).toBe('test-id');
        expect(response.newFlow.nodes!['test-id'].next).toBe('trigger-1');
      }
    });
  });

  describe('duplicateNode', () => {
    it('should return undefined when flow is null or undefined', () => {
      const { result } = renderHook(() => useFlowActions());

      expect(result.current.duplicateNode(null as any, 'node-1')).toBeUndefined();
      expect(result.current.duplicateNode(undefined as any, 'node-1')).toBeUndefined();
    });

    it('should return undefined when node is not found', () => {
      const { result } = renderHook(() => useFlowActions());

      const response = result.current.duplicateNode(mockFlow, 'non-existent-node');

      expect(response).toBeUndefined();
    });

    it('should duplicate a regular node successfully', () => {
      const { result } = renderHook(() => useFlowActions());

      const response = result.current.duplicateNode(mockFlow, 'node-1');

      expect(response).toBeDefined();
      expect(response!.nodes!['test-id']).toBeDefined();

      const duplicatedNode = response!.nodes!['test-id'];
      const originalNode = response!.nodes!['node-1']; // Use response flow, not original

      // Check that properties are copied correctly
      expect(duplicatedNode.name).toBe(mockFlow.nodes!['node-1'].name);
      expect(duplicatedNode.displayName).toBe(mockFlow.nodes!['node-1'].displayName);
      expect(duplicatedNode.icon).toBe(mockFlow.nodes!['node-1'].icon);
      expect(duplicatedNode.settings).toEqual(mockFlow.nodes!['node-1'].settings);

      // Check that the duplicated node is inserted in the chain correctly
      // The duplicated node should point to the original node's next
      expect(duplicatedNode.next).toBe(mockFlow.nodes!['node-1'].next); // Should point to node-2
      // The original node should now point to the duplicate
      expect(originalNode.next).toBe('test-id');
      // The duplicated node's prev should be the original node
      expect(duplicatedNode.prev).toBe('node-1');

      // Check that the next node's prev is updated
      expect(response!.nodes!['node-2'].prev).toBe('test-id');
    });

    it('should duplicate a trigger node successfully', () => {
      const { result } = renderHook(() => useFlowActions());

      const response = result.current.duplicateNode(mockFlow, 'trigger-1');

      expect(response).toBeDefined();
      expect(response!.triggers['test-id']).toBeDefined();

      const duplicatedTrigger = response!.triggers['test-id'];
      const originalTrigger = mockFlow.triggers['trigger-1'];

      // Check that properties are copied correctly
      expect(duplicatedTrigger.name).toBe(originalTrigger.name);
      expect(duplicatedTrigger.displayName).toBe(originalTrigger.displayName);
      expect(duplicatedTrigger.icon).toBe(originalTrigger.icon);
      expect(duplicatedTrigger.settings).toEqual(originalTrigger.settings);
      expect(duplicatedTrigger.prev).toBe(originalTrigger.prev);

      // Trigger should maintain its next reference
      expect(duplicatedTrigger.next).toBe('');
    });

    it('should handle node with no next node', () => {
      const { result } = renderHook(() => useFlowActions());

      const response = result.current.duplicateNode(mockFlow, 'node-2');

      expect(response).toBeDefined();
      expect(response!.nodes!['test-id']).toBeDefined();

      const duplicatedNode = response!.nodes!['test-id'];
      const originalNode = response!.nodes!['node-2']; // Use response flow

      // Check that the duplicated node has empty next (same as original)
      expect(duplicatedNode.next).toBe('');
      // The original node should now point to the duplicate
      expect(originalNode.next).toBe('test-id');
      // The duplicated node's prev should be the original node
      expect(duplicatedNode.prev).toBe('node-2');
    });

    it('should deep copy settings object', () => {
      const { result } = renderHook(() => useFlowActions());

      // Create a flow with a node that has complex settings
      const flowWithComplexSettings: Flow = {
        ...mockFlow,
        nodes: {
          ...mockFlow.nodes,
          'complex-node': {
            id: 'complex-node',
            name: FlowNodeType.Code,
            displayName: 'Complex Node',
            description: 'Node with complex settings',
            settings: {
              code: 'console.log("test")',
              variables: { input: 'value' },
              nested: { deep: { value: 'test' } }
            },
            prev: 'trigger-1',
            next: '',
          } as unknown as FlowNodeData,
        }
      };

      const response = result.current.duplicateNode(flowWithComplexSettings, 'complex-node');

      expect(response).toBeDefined();

      const duplicatedNode = response!.nodes!['test-id'];
      const originalNode = flowWithComplexSettings.nodes!['complex-node'];

      // Settings should be deep copied
      expect(duplicatedNode.settings).toEqual(originalNode.settings);
      expect(duplicatedNode.settings).not.toBe(originalNode.settings); // Different object reference

      // Modify duplicated settings to ensure they're independent
      (duplicatedNode.settings as any).code = 'modified';
      expect((originalNode.settings as any).code).toBe('console.log("test")');
    });

    it('should handle node with icon property', () => {
      const { result } = renderHook(() => useFlowActions());

      const flowWithIcon: Flow = {
        ...mockFlow,
        nodes: {
          ...mockFlow.nodes,
          'icon-node': {
            id: 'icon-node',
            name: FlowNodeType.Code,
            displayName: 'Icon Node',
            description: 'Node with icon',
            icon: '🚀',
            settings: {},
            prev: 'trigger-1',
            next: '',
          } as unknown as FlowNodeData,
        }
      };

      const response = result.current.duplicateNode(flowWithIcon, 'icon-node');

      expect(response).toBeDefined();

      const duplicatedNode = response!.nodes!['test-id'];
      expect(duplicatedNode.icon).toBe('🚀');
    });

    it('should handle trigger node with icon property', () => {
      const { result } = renderHook(() => useFlowActions());

      const flowWithIconTrigger: Flow = {
        ...mockFlow,
        triggers: {
          ...mockFlow.triggers,
          'icon-trigger': {
            id: 'icon-trigger',
            name: FlowNodeType.WebhookTrigger,
            displayName: 'Icon Trigger',
            description: 'Trigger with icon',
            icon: '⚡',
            settings: {},
            next: 'node-1',
          } as unknown as FlowNodeData,
        }
      };

      const response = result.current.duplicateNode(flowWithIconTrigger, 'icon-trigger');

      expect(response).toBeDefined();

      const duplicatedTrigger = response!.triggers['test-id'];
      expect(duplicatedTrigger.icon).toBe('⚡');
    });

    it('should not modify the original flow object', () => {
      const { result } = renderHook(() => useFlowActions());

      const originalFlowString = JSON.stringify(mockFlow);

      result.current.duplicateNode(mockFlow, 'node-1');

      // Original flow should remain unchanged
      expect(JSON.stringify(mockFlow)).toBe(originalFlowString);
    });

    it('should handle node in the middle of a chain', () => {
      const { result } = renderHook(() => useFlowActions());

      // Create a longer chain: trigger-1 -> node-1 -> node-2 -> node-3
      const chainFlow: Flow = {
        ...mockFlow,
        nodes: {
          'node-1': {
            id: 'node-1',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 1',
            description: 'First node',
            settings: {},
            prev: 'trigger-1',
            next: 'node-2',
          } as unknown as FlowNodeData,
          'node-2': {
            id: 'node-2',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 2',
            description: 'Middle node',
            settings: {},
            prev: 'node-1',
            next: 'node-3',
          } as unknown as FlowNodeData,
          'node-3': {
            id: 'node-3',
            name: FlowNodeType.EmptyNode,
            displayName: 'Node 3',
            description: 'Last node',
            settings: {},
            prev: 'node-2',
            next: '',
          } as unknown as FlowNodeData,
        }
      };

      const response = result.current.duplicateNode(chainFlow, 'node-2');

      expect(response).toBeDefined();

      // Check the chain is properly updated: node-1 -> node-2 -> test-id -> node-3
      expect(response!.nodes!['node-2'].next).toBe('test-id');
      expect(response!.nodes!['test-id'].next).toBe('node-3');
      expect(response!.nodes!['node-3'].prev).toBe('test-id');
    });
  });
});
