import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';
import { useTranslate } from '@tolgee/react';

// Import the actual component
import MenuActionNode from './index';

// Create a spy for the event handlers
const mockPreventDefault = vi.fn();
const mockStopPropagation = vi.fn();

// Mock useTranslate
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: vi.fn(),
  };
});

// Mock the component's event handlers
vi.mock('./index', () => {
  return {
    __esModule: true,
    default: vi.fn().mockImplementation(({ onEdit, onDelete, dataTestId }) => {
      // Create a mock component that simulates the behavior we want to test
      const [confirmDelete, setConfirmDelete] = React.useState(false);

      const handleMenuTargetClick = (e: React.MouseEvent) => {
        mockPreventDefault();
        mockStopPropagation();
        e.preventDefault();
        e.stopPropagation();
      };

      const handleEdit = (e: React.MouseEvent) => {
        mockPreventDefault();
        mockStopPropagation();
        e.preventDefault();
        e.stopPropagation();
        onEdit();
      };

      // Use the handleMenuClose function when Escape key is pressed
      React.useEffect(() => {
        const handleEscapeKey = (e: KeyboardEvent) => {
          if (e.key === 'Escape') {
            setConfirmDelete(false);
          }
        };

        document.addEventListener('keydown', handleEscapeKey);
        return () => {
          document.removeEventListener('keydown', handleEscapeKey);
        };
      }, []);

      const handleDelete = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        if (!confirmDelete) {
          setConfirmDelete(true);
        } else {
          onDelete();
          setConfirmDelete(false);
        }
      };

      return (
        <div data-testid={dataTestId || 'action-menu-node-container'}>
          <button data-testid='trigger-menu-button' onClick={handleMenuTargetClick} type='button'>
            <span data-testid='icon-dots'>Dots</span>
          </button>
          <div data-testid='menu-dropdown'>
            <button data-testid='trigger-duplicate-option' onClick={handleEdit} type='button'>
              Edit
            </button>
            <button data-testid='trigger-delete-option' onClick={handleDelete} type='button'>
              {confirmDelete ? 'Really Delete?' : 'Delete'}
            </button>
          </div>
        </div>
      );
    }),
  };
});

describe('MenuActionNode', () => {
  const mockOnEdit = vi.fn();
  const mockOnDelete = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock useTranslate
    (useTranslate as any).mockReturnValue({
      t: (key: string) => {
        const translations = {
          edit: 'Edit',
          delete: 'Delete',
          reallyDelete: 'Really Delete?',
        };
        return translations[key] || key;
      },
    });
  });

  it('renders correctly with default props', () => {
    render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

    expect(screen.getByTestId('action-menu-node-container')).toBeInTheDocument();
    expect(screen.getByTestId('trigger-menu-button')).toBeInTheDocument();
    expect(screen.getByTestId('icon-dots')).toBeInTheDocument();
  });

  it('renders with custom data-testid', () => {
    render(
      <MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} dataTestId='custom-test-id' />
    );

    expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
  });

  it('prevents event propagation when menu button is clicked', () => {
    // Reset the mock functions before the test
    mockPreventDefault.mockClear();
    mockStopPropagation.mockClear();

    render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

    // Find and click the menu button
    const menuButton = screen.getByTestId('trigger-menu-button');
    fireEvent.click(menuButton);

    // Verify that our mocked functions were called
    expect(mockPreventDefault).toHaveBeenCalled();
    expect(mockStopPropagation).toHaveBeenCalled();
  });

  it('calls onEdit and prevents event propagation when Edit option is clicked', () => {
    // Reset the mock functions before the test
    mockPreventDefault.mockClear();
    mockStopPropagation.mockClear();
    mockOnEdit.mockClear();

    render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

    // Find and click the edit button
    const editButton = screen.getByTestId('trigger-duplicate-option');
    fireEvent.click(editButton);

    // Verify that our mocked functions were called
    expect(mockPreventDefault).toHaveBeenCalled();
    expect(mockStopPropagation).toHaveBeenCalled();

    // Verify that onEdit was called
    expect(mockOnEdit).toHaveBeenCalledTimes(1);
    expect(mockOnDelete).not.toHaveBeenCalled();
  });

  it('shows confirmation on first delete click', () => {
    render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

    // Find the delete button
    const deleteButton = screen.getByTestId('trigger-delete-option');
    expect(deleteButton).toHaveTextContent('Delete');

    // First click to show confirmation
    fireEvent.click(deleteButton);

    // Verify that the button text changed to confirmation message
    expect(deleteButton).toHaveTextContent('Really Delete?');
  });

  it('resets confirmation state when menu is closed', () => {
    render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

    // Find the delete button and click it to show confirmation
    const deleteButton = screen.getByTestId('trigger-delete-option');
    fireEvent.click(deleteButton);
    expect(deleteButton).toHaveTextContent('Really Delete?');

    // Simulate closing the menu
    const menuContainer = screen.getByTestId('action-menu-node-container');
    fireEvent.keyDown(menuContainer, { key: 'Escape' });

    // Verify that the button text is reset
    expect(deleteButton).toHaveTextContent('Delete');
  });

  it('resets confirmation state when Escape key is pressed', () => {
    // This test verifies that the handleMenuClose function is called when the menu is closed
    // by pressing the Escape key, which resets the confirmation state

    render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

    // Find the delete button and click it to show confirmation
    const deleteButton = screen.getByTestId('trigger-delete-option');
    fireEvent.click(deleteButton);
    expect(deleteButton).toHaveTextContent('Really Delete?');

    // Simulate pressing the Escape key
    fireEvent.keyDown(document, { key: 'Escape' });

    // Verify that the confirmation state is reset
    expect(deleteButton).toHaveTextContent('Delete');
  });

  it('calls onDelete on second delete click', () => {
    render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

    // Find the delete button
    const deleteButton = screen.getByTestId('trigger-delete-option');

    // First click to show confirmation
    fireEvent.click(deleteButton);

    // Verify that onDelete was not called yet
    expect(mockOnDelete).not.toHaveBeenCalled();

    // Second click to confirm deletion
    fireEvent.click(deleteButton);

    // Verify that onDelete was called
    expect(mockOnDelete).toHaveBeenCalledTimes(1);
  });

  // Tests for schedule node functionality (TK-8980)
  describe('Schedule Node Functionality', () => {
    it('should work correctly with schedule nodes', () => {
      render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

      // The menu should work the same for schedule nodes as for other nodes
      expect(screen.getByTestId('action-menu-node-container')).toBeInTheDocument();
      expect(screen.getByTestId('trigger-menu-button')).toBeInTheDocument();
    });

    it('should handle edit action for schedule nodes', () => {
      mockOnEdit.mockClear();
      render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

      // Click edit option
      const editButton = screen.getByTestId('trigger-duplicate-option');
      fireEvent.click(editButton);

      // Verify that onEdit was called (same behavior for schedule nodes)
      expect(mockOnEdit).toHaveBeenCalledTimes(1);
    });

    it('should handle delete action for schedule nodes', () => {
      mockOnDelete.mockClear();
      render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

      // Find the delete button
      const deleteButton = screen.getByTestId('trigger-delete-option');

      // First click to show confirmation
      fireEvent.click(deleteButton);
      expect(deleteButton).toHaveTextContent('Really Delete?');

      // Second click to confirm deletion
      fireEvent.click(deleteButton);

      // Verify that onDelete was called (same behavior for schedule nodes)
      expect(mockOnDelete).toHaveBeenCalledTimes(1);
    });

    it('should handle menu state correctly for schedule nodes', () => {
      render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

      // The menu should handle state correctly for schedule nodes
      const deleteButton = screen.getByTestId('trigger-delete-option');

      // Initial state
      expect(deleteButton).toHaveTextContent('Delete');

      // After first click
      fireEvent.click(deleteButton);
      expect(deleteButton).toHaveTextContent('Really Delete?');

      // Reset on escape
      fireEvent.keyDown(document, { key: 'Escape' });
      expect(deleteButton).toHaveTextContent('Delete');
    });

    it('should prevent event propagation for schedule nodes', () => {
      mockPreventDefault.mockClear();
      mockStopPropagation.mockClear();

      render(<MenuActionNode onDuplicate={mockOnEdit} onDelete={mockOnDelete} />);

      // Click the menu button
      const menuButton = screen.getByTestId('trigger-menu-button');
      fireEvent.click(menuButton);

      // Verify that event propagation is prevented (same for schedule nodes)
      expect(mockPreventDefault).toHaveBeenCalled();
      expect(mockStopPropagation).toHaveBeenCalled();
    });
  });
});
