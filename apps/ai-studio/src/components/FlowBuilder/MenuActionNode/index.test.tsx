import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useTranslate } from '@tolgee/react';
import { mockLibraries, renderWithMantine } from '@/utils/test';

// Import the actual component
import MenuActionNode from './index';

mockLibraries();

// Mock useTranslate
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: vi.fn(),
  };
});

describe('MenuActionNode', () => {
  const mockOnDuplicate = vi.fn();
  const mockOnDelete = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock useTranslate
    (useTranslate as any).mockReturnValue({
      t: (key: string) => {
        const translations = {
          duplicate: 'Duplicate',
          delete: 'Delete',
          reallyDelete: 'Really Delete?',
        };
        return translations[key] || key;
      },
    });
  });

  it('renders correctly with default props', () => {
    renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

    expect(screen.getByTestId('action-menu-node-container')).toBeInTheDocument();
    // The button is inside the Menu.Target, so we need to find it by role
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('renders with custom data-testid', () => {
    renderWithMantine(
      <MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} dataTestId='custom-test-id' />
    );

    expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
  });

  it('opens menu when menu button is clicked', async () => {
    const user = userEvent.setup();
    renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

    // Find and click the menu button by role
    const menuButton = screen.getByRole('button');
    await user.click(menuButton);

    // Verify that menu items are visible
    await waitFor(() => {
      expect(screen.getByTestId('trigger-duplicate-option')).toBeInTheDocument();
      expect(screen.getByTestId('trigger-delete-option')).toBeInTheDocument();
    });
  });

  it('calls onDuplicate when Duplicate option is clicked', async () => {
    const user = userEvent.setup();
    renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

    // Open the menu first
    const menuButton = screen.getByRole('button');
    await user.click(menuButton);

    // Find and click the duplicate button
    await waitFor(() => {
      expect(screen.getByTestId('trigger-duplicate-option')).toBeInTheDocument();
    });

    const duplicateButton = screen.getByTestId('trigger-duplicate-option');
    await user.click(duplicateButton);

    // Verify that onDuplicate was called
    expect(mockOnDuplicate).toHaveBeenCalledTimes(1);
    expect(mockOnDelete).not.toHaveBeenCalled();
  });

  it('shows confirmation on first delete click', async () => {
    const user = userEvent.setup();
    renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

    // Open the menu first
    const menuButton = screen.getByRole('button');
    await user.click(menuButton);

    // Find the delete button
    await waitFor(() => {
      expect(screen.getByTestId('trigger-delete-option')).toBeInTheDocument();
    });

    const deleteButton = screen.getByTestId('trigger-delete-option');
    expect(deleteButton).toHaveTextContent('Delete');

    // First click to show confirmation
    await user.click(deleteButton);

    // Verify that the button text changed to confirmation message
    expect(deleteButton).toHaveTextContent('Really Delete?');
  });

  it('resets confirmation state when menu is closed', async () => {
    const user = userEvent.setup();
    renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

    // Open the menu first
    const menuButton = screen.getByRole('button');
    await user.click(menuButton);

    // Find the delete button and click it to show confirmation
    await waitFor(() => {
      expect(screen.getByTestId('trigger-delete-option')).toBeInTheDocument();
    });

    const deleteButton = screen.getByTestId('trigger-delete-option');
    await user.click(deleteButton);
    expect(deleteButton).toHaveTextContent('Really Delete?');

    // Close the menu by pressing Escape
    await user.keyboard('{Escape}');

    // Reopen menu to check if state was reset
    await user.click(menuButton);
    await waitFor(() => {
      expect(screen.getByTestId('trigger-delete-option')).toBeInTheDocument();
    });

    const resetDeleteButton = screen.getByTestId('trigger-delete-option');
    expect(resetDeleteButton).toHaveTextContent('Delete');
  });

  it('calls onDelete on second delete click', async () => {
    const user = userEvent.setup();
    renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

    // Open the menu first
    const menuButton = screen.getByRole('button');
    await user.click(menuButton);

    // Find the delete button
    await waitFor(() => {
      expect(screen.getByTestId('trigger-delete-option')).toBeInTheDocument();
    });

    const deleteButton = screen.getByTestId('trigger-delete-option');

    // First click to show confirmation
    await user.click(deleteButton);

    // Verify that onDelete was not called yet
    expect(mockOnDelete).not.toHaveBeenCalled();

    // Second click to confirm deletion
    await user.click(deleteButton);

    // Verify that onDelete was called
    expect(mockOnDelete).toHaveBeenCalledTimes(1);
  });

  // Tests for schedule node functionality (TK-8980)
  describe('Schedule Node Functionality', () => {
    it('should work correctly with schedule nodes', () => {
      renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

      // The menu should work the same for schedule nodes as for other nodes
      expect(screen.getByTestId('action-menu-node-container')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should handle duplicate action for schedule nodes', async () => {
      const user = userEvent.setup();
      mockOnDuplicate.mockClear();
      renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

      // Open the menu first
      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      // Click duplicate option
      await waitFor(() => {
        expect(screen.getByTestId('trigger-duplicate-option')).toBeInTheDocument();
      });

      const duplicateButton = screen.getByTestId('trigger-duplicate-option');
      await user.click(duplicateButton);

      // Verify that onDuplicate was called (same behavior for schedule nodes)
      expect(mockOnDuplicate).toHaveBeenCalledTimes(1);
    });

    it('should handle delete action for schedule nodes', async () => {
      const user = userEvent.setup();
      mockOnDelete.mockClear();
      renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

      // Open the menu first
      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      // Find the delete button
      await waitFor(() => {
        expect(screen.getByTestId('trigger-delete-option')).toBeInTheDocument();
      });

      const deleteButton = screen.getByTestId('trigger-delete-option');

      // First click to show confirmation
      await user.click(deleteButton);
      expect(deleteButton).toHaveTextContent('Really Delete?');

      // Second click to confirm deletion
      await user.click(deleteButton);

      // Verify that onDelete was called (same behavior for schedule nodes)
      expect(mockOnDelete).toHaveBeenCalledTimes(1);
    });

    it('should handle menu state correctly for schedule nodes', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

      // Open the menu first
      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      // The menu should handle state correctly for schedule nodes
      await waitFor(() => {
        expect(screen.getByTestId('trigger-delete-option')).toBeInTheDocument();
      });

      const deleteButton = screen.getByTestId('trigger-delete-option');

      // Initial state
      expect(deleteButton).toHaveTextContent('Delete');

      // After first click
      await user.click(deleteButton);
      expect(deleteButton).toHaveTextContent('Really Delete?');

      // Reset on escape
      await user.keyboard('{Escape}');

      // Reopen menu to check if state was reset
      await user.click(menuButton);
      await waitFor(() => {
        expect(screen.getByTestId('trigger-delete-option')).toBeInTheDocument();
      });

      const resetDeleteButton = screen.getByTestId('trigger-delete-option');
      expect(resetDeleteButton).toHaveTextContent('Delete');
    });

    it('should handle disabled duplicate option when onDuplicate is not provided', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MenuActionNode onDelete={mockOnDelete} />);

      // The menu should render correctly even without onDuplicate
      expect(screen.getByTestId('action-menu-node-container')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeInTheDocument();

      // Open the menu
      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      // Check that duplicate option is disabled
      await waitFor(() => {
        const duplicateButton = screen.getByTestId('trigger-duplicate-option');
        expect(duplicateButton).toBeInTheDocument();
        expect(duplicateButton).toHaveAttribute('data-disabled', 'true');
      });
    });

    it('should close menu when clicking outside', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MenuActionNode onDuplicate={mockOnDuplicate} onDelete={mockOnDelete} />);

      // Open the menu
      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      // Verify menu is open
      await waitFor(() => {
        expect(screen.getByTestId('trigger-duplicate-option')).toBeInTheDocument();
      });

      // Click outside to close menu
      await user.click(document.body);

      // Verify menu is closed
      await waitFor(() => {
        expect(screen.queryByTestId('trigger-duplicate-option')).not.toBeInTheDocument();
      });
    });
  });
});
