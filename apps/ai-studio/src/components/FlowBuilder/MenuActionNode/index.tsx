import { ActionIcon, Menu } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCopy, IconDots, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useState, type BaseSyntheticEvent } from 'react';

const useStyles = createStyles((theme) => ({
  customDotsIcon: {
    borderRadius: 999,
    '&:hover': {
      backgroundColor: theme.colors.decaLight[1],
    },
  },
  deleteMenuItem: {
    '&:hover': {
      backgroundColor: 'var(--mantine-color-gray-1) !important',
    },
  },
}));

const MenuActionNode = ({
  onDuplicate,
  onDelete,
  dataTestId = 'action-menu-node-container',
}: {
  onDuplicate?: () => void;
  onDelete: () => void;
  dataTestId?: string;
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('flow');
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [menuOpened, setMenuOpened] = useState(false);

  const handleMenuClose = useCallback(() => {
    setMenuOpened(false);
    setConfirmDelete(false);
  }, []);

  const handleDelete = useCallback(
    (e: BaseSyntheticEvent) => {
      // Prevent the menu from closing on the first click
      if (!confirmDelete) {
        e.preventDefault();
        e.stopPropagation();
        setConfirmDelete(true);
        return false; // Try to prevent default behavior
      }

      e.preventDefault();
      e.stopPropagation();
      onDelete();
      setConfirmDelete(false);
      handleMenuClose();
    },
    [confirmDelete, onDelete, handleMenuClose]
  );

  const handleDuplicate = useCallback(
    (e: BaseSyntheticEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onDuplicate?.();
      handleMenuClose();
    },
    [onDuplicate, handleMenuClose]
  );

  const handleMenuTargetClick = useCallback((e: BaseSyntheticEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  return (
    <Menu
      width={250}
      withinPortal
      opened={menuOpened}
      closeOnEscape={true}
      position='bottom-start'
      data-testid={dataTestId}
      onChange={setMenuOpened}
      onClose={handleMenuClose}
      closeOnClickOutside={true}
      closeOnItemClick={confirmDelete}
    >
      <Menu.Target>
        <ActionIcon
          variant='subtle'
          className={classes.customDotsIcon}
          data-testid='trigger-menu-button'
          onClick={handleMenuTargetClick}
          size={20}
          color='decaGrey.4'
        >
          <IconDots size={16} />
        </ActionIcon>
      </Menu.Target>
      <Menu.Dropdown>
        <Menu.Item
          disabled={!onDuplicate}
          onClick={handleDuplicate}
          leftSection={<IconCopy size={14} />}
          data-testid='trigger-duplicate-option'
        >
          {t('duplicate')}
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item
          onClick={handleDelete}
          leftSection={<IconTrash size={14} />}
          color='var(--mantine-color-decaRed-5)'
          data-testid='trigger-delete-option'
          closeMenuOnClick={confirmDelete}
          className={classes.deleteMenuItem}
        >
          {confirmDelete ? t('reallyDelete') : t('delete')}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

export default MenuActionNode;
