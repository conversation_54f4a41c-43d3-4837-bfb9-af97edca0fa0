import { useCallback } from 'react';
import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import MenuActionNode from '../MenuActionNode';
import BaseSelectNode from './BaseSelectNode';
import { FlowTypeNode, FlowNodeType } from '@/models/flow';
import { useTranslate } from '@tolgee/react';
import { CatalogNodeIcon } from '@resola-ai/ui';

interface SubPathNodeProps {
  id: string;
  data?: {
    onEdit?: (id: string) => void;
    nextNodeId?: string;
    parentNodeId?: string;
    orderedNumber?: number;
    actualParentNodeId?: string;
  };
}

export default function SubPathNode({ id, data }: SubPathNodeProps) {
  const { t } = useTranslate('flow');
  const { handleOpenFormPanel: handleOpenRightPanel, flowActionHandlers } = useFlowBuilderContext();
  const onEdit = useCallback(() => {
    handleOpenRightPanel({
      nodeId: id,
      orderNumber: data?.orderedNumber ?? 0,
      type: FlowNodeType.SubPathNode,
      parentNodeId: data?.actualParentNodeId ?? '',
    });
  }, [data, id]);

  const onDelete = useCallback(() => {
    flowActionHandlers.handleRemoveNode({
      nodeId: data?.nextNodeId ?? '',
      typeRemove: FlowTypeNode.SubPath,
      parentId: data?.actualParentNodeId ?? '',
    });
  }, [flowActionHandlers, id, data]);

  const orderedNumber = data?.orderedNumber ?? 0;

  return (
    <BaseSelectNode
      id={id}
      title='Path'
      icon='arrow-split'
      customIcon={<CatalogNodeIcon name='virtual-node-sub-path' size={20} />}
      openModalAction={onEdit}
      dataTestId='main-path-node'
      description={`${orderedNumber}. ${t('defaultSubPathName')}`}
      menuContent={<MenuActionNode onDelete={onDelete} />}
    />
  );
}
