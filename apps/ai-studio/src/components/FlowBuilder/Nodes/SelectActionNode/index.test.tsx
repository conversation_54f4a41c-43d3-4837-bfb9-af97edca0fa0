import { screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import userEvent from '@testing-library/user-event';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import SelectActionNode from './index';
import { FlowBuilderProvider } from '@/contexts/FlowBuilderContext';
import { useTranslate } from '@tolgee/react';
import { FlowNodeType } from '@/models/flow';

mockLibraries();

const mockOpenCatalogForReplaceEmptyNode = vi.fn();
const mockHandleOpenFormPanel = vi.fn();
const mockHandleRemoveNode = vi.fn();
const mockHandleDuplicateNode = vi.fn();

// Mock useTranslate
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: vi.fn(),
  };
});

// Mock useFlowSchema hook
vi.mock('@/hooks/useFlowSchema', () => ({
  __esModule: true,
  default: vi.fn(() => ({
    schema: {
      displayName: 'Test Action',
      icon: 'test-icon',
    },
  })),
}));

// Mock FlowBuilderContext
vi.mock('@/contexts/FlowBuilderContext', () => ({
  useFlowBuilderContext: () => ({
    flowActionHandlers: {
      handleRemoveNode: mockHandleRemoveNode,
      handleDuplicateNode: mockHandleDuplicateNode,
    },
    handleOpenCatalogForReplaceEmptyNode: mockOpenCatalogForReplaceEmptyNode,
    handleOpenFormPanel: mockHandleOpenFormPanel,
  }),
  FlowBuilderProvider: ({ children }) => <>{children}</>,
}));

// Mock MenuActionNode component
vi.mock('../../MenuActionNode', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({ onDuplicate, onDelete, dataTestId }) => {
    return (
      <div data-testid={dataTestId || 'menu-action-node'}>
        <button
          type='button'
          data-testid='duplicate-button'
          data-action='duplicate'
          onClick={onDuplicate}
        >
          Duplicate
        </button>
        <button type='button' data-testid='delete-button' data-action='delete' onClick={onDelete}>
          Delete
        </button>
      </div>
    );
  }),
}));

// Mock BaseSelectNode component
vi.mock('../BaseSelectNode', () => ({
  __esModule: true,
  default: vi
    .fn()
    .mockImplementation(({ title, description, dataTestId, menuContent, openModalAction }) => (
      <div data-testid={dataTestId || 'base-select-node'}>
        <div data-testid='title'>{title}</div>
        <div data-testid='description'>{description}</div>
        <div data-testid='icon-bolt'>Icon Bolt</div>
        <button data-testid='node-click-button' onClick={openModalAction}>
          Open Modal
        </button>
        <div data-testid='menu-content'>{menuContent}</div>
      </div>
    )),
}));

describe('SelectActionNode', () => {
  const nodeId = 'test-node';
  const parentNodeId = 'parent-node';

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock useTranslate
    (useTranslate as any).mockReturnValue({
      t: (key: string) => {
        const translations = {
          defaultTitleActionNode: 'Select an action',
          defaultNameActionNode: 'Select an action for your flow',
          defaultTitleTriggerNode: 'Select a trigger',
          defaultNameTriggerNode: 'Select an event for your flow to run',
        };
        return translations[key] || key;
      },
    });
  });

  // Helper function to render with FlowBuilderProvider
  const renderWithProvider = (props: any) => {
    return renderWithMantine(
      <FlowBuilderProvider>
        <SelectActionNode {...props} />
      </FlowBuilderProvider>
    );
  };

  it('renders the node with correct text content', () => {
    renderWithProvider({ id: nodeId });
    expect(screen.getByText('0. Select an action for your flow')).toBeInTheDocument();
  });

  it('renders with correct data-testid', () => {
    renderWithProvider({ id: nodeId });
    expect(screen.getByTestId('text-action')).toBeInTheDocument();
  });

  it('renders icon', () => {
    renderWithProvider({ id: nodeId });
    expect(screen.getByTestId('icon-bolt')).toBeInTheDocument();
  });

  it('opens catalog when node is clicked for EmptyNode', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: { orderedNumber: 1, actualParentNodeId: parentNodeId },
      type: FlowNodeType.EmptyNode,
    });

    const nodeButton = screen.getByTestId('node-click-button');
    await user.click(nodeButton);

    expect(mockOpenCatalogForReplaceEmptyNode).toHaveBeenCalledTimes(1);
    expect(mockOpenCatalogForReplaceEmptyNode).toHaveBeenCalledWith({
      nodeId,
      triggerContext: false,
    });
  });

  it('opens catalog when node is clicked for NewTrigger', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: { orderedNumber: 1, actualParentNodeId: parentNodeId },
      type: FlowNodeType.NewTrigger,
    });

    const nodeButton = screen.getByTestId('node-click-button');
    await user.click(nodeButton);

    expect(mockOpenCatalogForReplaceEmptyNode).toHaveBeenCalledTimes(1);
    expect(mockOpenCatalogForReplaceEmptyNode).toHaveBeenCalledWith({
      nodeId,
      triggerContext: true,
    });
  });

  it('opens form panel when node is clicked for other node types', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: { orderedNumber: 1, actualParentNodeId: parentNodeId },
      type: FlowNodeType.Code,
    });

    const nodeButton = screen.getByTestId('node-click-button');
    await user.click(nodeButton);

    expect(mockHandleOpenFormPanel).toHaveBeenCalledTimes(1);
    expect(mockHandleOpenFormPanel).toHaveBeenCalledWith({
      nodeId,
      orderNumber: 1,
      parentNodeId,
      type: FlowNodeType.Code,
    });
  });

  it('calls handleDuplicateNode when duplicate button is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: { orderedNumber: 1, actualParentNodeId: parentNodeId },
    });

    const duplicateButton = screen.getByTestId('duplicate-button');
    await user.click(duplicateButton);

    expect(mockHandleDuplicateNode).toHaveBeenCalledTimes(1);
    expect(mockHandleDuplicateNode).toHaveBeenCalledWith({ nodeId });
  });

  it('calls handleRemoveNode when delete button is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: { actualParentNodeId: parentNodeId, isTriggerNode: false },
    });

    const deleteButton = screen.getByTestId('delete-button');
    await user.click(deleteButton);

    expect(mockHandleRemoveNode).toHaveBeenCalledTimes(1);
    expect(mockHandleRemoveNode).toHaveBeenCalledWith({
      nodeId,
      parentId: parentNodeId,
      typeRemove: 'node',
    });
  });

  it('calls handleRemoveNode with TriggerNode type when isTriggerNode is true', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: { actualParentNodeId: parentNodeId, isTriggerNode: true },
    });

    const deleteButton = screen.getByTestId('delete-button');
    await user.click(deleteButton);

    expect(mockHandleRemoveNode).toHaveBeenCalledTimes(1);
    expect(mockHandleRemoveNode).toHaveBeenCalledWith({
      nodeId,
      parentId: parentNodeId,
      typeRemove: 'trigger-node',
    });
  });

  it('handles missing actualParentNodeId gracefully', async () => {
    const user = userEvent.setup();

    renderWithProvider({ id: nodeId, data: {} });

    const deleteButton = screen.getByTestId('delete-button');
    await user.click(deleteButton);

    expect(mockHandleRemoveNode).toHaveBeenCalledTimes(1);
    expect(mockHandleRemoveNode).toHaveBeenCalledWith({
      nodeId,
      parentId: '',
      typeRemove: 'node',
    });
  });

  it('displays correct title and description for different node types', () => {
    // Test EmptyNode
    renderWithProvider({
      id: nodeId,
      type: FlowNodeType.EmptyNode,
      data: { orderedNumber: 1 },
    });

    expect(screen.getByTestId('title')).toHaveTextContent('Select an action');
    expect(screen.getByTestId('description')).toHaveTextContent(
      '1. Select an action for your flow'
    );
  });

  it('displays correct title and description for NewTrigger', () => {
    renderWithProvider({
      id: nodeId,
      type: FlowNodeType.NewTrigger,
      data: { orderedNumber: 2 },
    });

    expect(screen.getByTestId('title')).toHaveTextContent('Select a trigger');
    expect(screen.getByTestId('description')).toHaveTextContent(
      '2. Select an event for your flow to run'
    );
  });

  it('uses custom displayName when provided', () => {
    renderWithProvider({
      id: nodeId,
      type: FlowNodeType.Code,
      data: {
        orderedNumber: 3,
        displayName: 'Custom Action Name',
      },
    });

    expect(screen.getByTestId('description')).toHaveTextContent('3. Custom Action Name');
  });
});
