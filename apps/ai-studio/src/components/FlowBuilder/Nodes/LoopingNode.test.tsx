import { screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import LoopingNode from './LoopingNode';
import { FlowNodeType, FlowTypeNode } from '@/models/flow';
import { useTranslate } from '@tolgee/react';

mockLibraries();

// Mock useTranslate
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: vi.fn(),
  };
});

// Mock the FlowBuilderContext
const mockHandleRemoveNode = vi.fn();
const mockHandleOpenFormPanel = vi.fn();
const mockOnEdit = vi.fn();
const mockOnDelete = vi.fn();

// Create a variable to control the currentSelectNodeId value
let currentSelectNodeId = 'test-node';

vi.mock('@/contexts/FlowBuilderContext', () => ({
  useFlowBuilderContext: () => ({
    flowActionHandlers: {
      handleRemoveNode: mockHandleRemoveNode,
    },
    handleOpenFormPanel: mockHandleOpenFormPanel,
    currentSelectNodeId,
  }),
}));

// Mock the BaseSelectNode component
vi.mock('./BaseSelectNode', () => ({
  __esModule: true,
  default: vi
    .fn()
    .mockImplementation(({ title, description, menuContent, openModalAction, baseStyle }) => (
      <button
        type='button'
        data-testid='base-select-node'
        onClick={openModalAction}
        className={baseStyle}
      >
        <div data-testid='title'>{title}</div>
        <div data-testid='description'>{description}</div>
        <div data-testid='icon'>Icon</div>
        <div data-testid='menu-content'>{menuContent}</div>
      </button>
    )),
}));

// Mock the MenuActionNode component
vi.mock('../MenuActionNode', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({ onEdit, onDelete }) => (
    <div data-testid='menu-action-node'>
      <button type='button' data-testid='edit-button' onClick={onEdit}>
        Edit
      </button>
      <button type='button' data-testid='delete-button' onClick={onDelete}>
        Delete
      </button>
    </div>
  )),
}));

// Mock the Flex component
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    Flex: ({ children, ...props }) => (
      <div data-testid='flex-container' {...props}>
        {children}
      </div>
    ),
  };
});

describe('LoopingNode', () => {
  const defaultProps = {
    id: 'test-node',
    data: {
      onEdit: mockOnEdit,
      onDelete: mockOnDelete,
      orderedNumber: 1,
      parentNodeId: 'parent-node',
      actualParentNodeId: 'actual-parent-node',
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset the currentSelectNodeId for each test
    currentSelectNodeId = 'test-node';
    // Mock useTranslate
    (useTranslate as any).mockReturnValue({
      t: (key: string) => {
        const translations = {
          loopingLabel: 'Looping',
          defaultNameActionNode: 'Select an event for your flow to run',
        };
        return translations[key] || key;
      },
    });
  });

  // Helper function to render the component
  const renderComponent = (props = defaultProps) => {
    return renderWithMantine(<LoopingNode {...props} />);
  };

  it('renders the node with correct structure', () => {
    renderComponent();

    // Check for the main container
    expect(screen.getByTestId('looping-node')).toBeInTheDocument();

    // Check for the BaseSelectNode
    expect(screen.getByTestId('base-select-node')).toBeInTheDocument();

    // Check for the title and description
    expect(screen.getByTestId('title')).toHaveTextContent('Loop');
    expect(screen.getByTestId('description')).toHaveTextContent(
      '1. Select an event for your flow to run'
    );

    // Check for the menu content
    expect(screen.getByTestId('menu-content')).toBeInTheDocument();
  });

  it('calls handleOpenFormPanel when clicked', () => {
    renderComponent();

    // Find and click the node
    const node = screen.getByTestId('base-select-node');
    fireEvent.click(node);

    // Verify that handleOpenFormPanel was called with the correct ID
    expect(mockHandleOpenFormPanel).toHaveBeenCalledWith({
      nodeId: 'test-node',
      orderNumber: 1,
      parentNodeId: 'actual-parent-node',
      type: FlowNodeType.Looping,
    });
  });

  it('calls onEdit when edit button is clicked', () => {
    renderComponent();

    // Find and click the edit button
    const editButton = screen.getByTestId('edit-button');
    fireEvent.click(editButton);

    // Verify that onEdit was called with the correct ID
    expect(mockHandleOpenFormPanel).toHaveBeenCalledWith({
      nodeId: 'test-node',
      orderNumber: 1,
      parentNodeId: 'actual-parent-node',
      type: FlowNodeType.Looping,
    });
  });

  it('calls handleRemoveNode when delete button is clicked', () => {
    renderComponent();

    // Find and click the delete button
    const deleteButton = screen.getByTestId('delete-button');
    fireEvent.click(deleteButton);

    // Verify that handleRemoveNode was called with the correct arguments
    expect(mockHandleRemoveNode).toHaveBeenCalledWith({
      nodeId: 'test-node',
      typeRemove: FlowTypeNode.Looping,
      parentId: 'actual-parent-node',
    });
  });

  it('handles missing orderedNumber by using default value', () => {
    const propsWithoutOrderedNumber = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        orderedNumber: undefined,
      },
    };

    renderComponent(propsWithoutOrderedNumber as any);

    // Check that the description uses the default orderedNumber (0)
    expect(screen.getByTestId('description')).toHaveTextContent(
      '0. Select an event for your flow to run'
    );
  });

  it('applies different style when not selected', () => {
    // Set the currentSelectNodeId to a different value before rendering
    currentSelectNodeId = 'different-node';

    renderComponent();

    // The baseStyle should be applied when not selected
    // We can't directly test the style, but we can verify the component rendered
    expect(screen.getByTestId('base-select-node')).toBeInTheDocument();
  });

  it('renders the LayerBehinds component', () => {
    renderComponent();

    // We can't directly test for the LayerBehinds component since it doesn't have test IDs,
    // but we can check that the main container has children
    const container = screen.getByTestId('looping-node');
    expect(container.children.length).toBeGreaterThan(0);
  });

  it('displays custom displayName when provided', () => {
    const propsWithDisplayName = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        displayName: 'Custom Loop Name',
      },
    };

    renderComponent(propsWithDisplayName);

    // Check that the description uses the custom displayName
    expect(screen.getByTestId('description')).toHaveTextContent('1. Custom Loop Name');
  });

  it('handles missing data gracefully', () => {
    const propsWithoutData = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        displayName: undefined,
      },
    };

    renderComponent(propsWithoutData);

    // Should fall back to default name
    expect(screen.getByTestId('description')).toHaveTextContent(
      '1. Select an event for your flow to run'
    );
  });
});
