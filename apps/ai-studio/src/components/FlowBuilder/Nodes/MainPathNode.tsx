import { useCallback } from 'react';
import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import MenuActionNode from '../MenuActionNode';
import BaseSelectNode from './BaseSelectNode';
import { FlowTypeNode } from '@/models/flow';
import { useTranslate } from '@tolgee/react';

interface MainPathNodeProps {
  id: string;
  data?: {
    onEdit?: (id: string) => void;
    onDelete?: (id: string) => void;
    orderedNumber?: number;
    actualParentNodeId: string;
  };
}

export default function MainPathNode({ id, data }: MainPathNodeProps) {
  const { t } = useTranslate('flow');
  const { flowActionHandlers } = useFlowBuilderContext();

  const onDelete = useCallback(() => {
    flowActionHandlers.handleRemoveNode({
      nodeId: id,
      typeRemove: FlowTypeNode.Path,
      parentId: data?.actualParentNodeId,
    });
  }, [flowActionHandlers, id, data?.actualParentNodeId]);
  const orderedNumber = data?.orderedNumber ?? 0;

  return (
    <BaseSelectNode
      id={id}
      title={t('pathLabel')}
      description={`${orderedNumber}. ${t('defaultPathName')}`}
      dataTestId='main-path-node'
      menuContent={<MenuActionNode onDelete={onDelete} />}
    />
  );
}
