import { screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import MainPathNode from './MainPathNode';
import { FlowTypeNode } from '@/models/flow';
import { useTranslate } from '@tolgee/react';

mockLibraries();

// Mock useTranslate
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: vi.fn(),
  };
});

// Mock the FlowBuilderContext
const mockHandleRemoveNode = vi.fn();

vi.mock('@/contexts/FlowBuilderContext', () => ({
  useFlowBuilderContext: () => ({
    flowActionHandlers: {
      handleRemoveNode: mockHandleRemoveNode,
    },
  }),
}));

// Mock the BaseSelectNode component
vi.mock('./BaseSelectNode', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({ title, description, menuContent, dataTestId }) => (
    <div data-testid={dataTestId}>
      <div data-testid='title'>{title}</div>
      <div data-testid='description'>{description}</div>
      <div data-testid='menu-content'>{menuContent}</div>
    </div>
  )),
}));

// Mock the MenuActionNode component
vi.mock('../MenuActionNode', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({ onDuplicate, onDelete }) => (
    <div data-testid='menu-action-node'>
      {onDuplicate && (
        <button type='button' data-testid='duplicate-button' onClick={onDuplicate}>
          Duplicate
        </button>
      )}
      <button type='button' data-testid='delete-button' onClick={onDelete}>
        Delete
      </button>
    </div>
  )),
}));

describe('MainPathNode', () => {
  const mockOnDelete = vi.fn();

  const defaultProps = {
    id: 'test-node',
    data: {
      onDelete: mockOnDelete,
      orderedNumber: 1,
      actualParentNodeId: 'actual-parent-node',
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock useTranslate
    (useTranslate as any).mockReturnValue({
      t: (key: string) => {
        const translations = {
          pathLabel: 'Path',
          defaultPathName: 'Default path name',
        };
        return translations[key] || key;
      },
    });
  });

  // Helper function to render the component
  const renderComponent = (props = defaultProps) => {
    return renderWithMantine(<MainPathNode {...props} />);
  };

  it('renders the node with correct structure', () => {
    renderComponent();

    // Check for the main container
    expect(screen.getByTestId('main-path-node')).toBeInTheDocument();

    // Check for the title and description
    expect(screen.getByTestId('title')).toHaveTextContent('Path');
    expect(screen.getByTestId('description')).toHaveTextContent('1. Default path name');

    // Check for the menu content
    expect(screen.getByTestId('menu-content')).toBeInTheDocument();
  });

  it('does not render duplicate button since onDuplicate is not provided', () => {
    renderComponent();

    // Verify that duplicate button is not rendered since MainPathNode doesn't provide onDuplicate
    expect(screen.queryByTestId('duplicate-button')).not.toBeInTheDocument();

    // But delete button should be present
    expect(screen.getByTestId('delete-button')).toBeInTheDocument();
  });

  it('calls handleRemoveNode when delete button is clicked', () => {
    renderComponent();

    // Find and click the delete button
    const deleteButton = screen.getByTestId('delete-button');
    fireEvent.click(deleteButton);

    // Verify that handleRemoveNode was called with the correct arguments
    expect(mockHandleRemoveNode).toHaveBeenCalledWith({
      nodeId: 'test-node',
      typeRemove: FlowTypeNode.Path,
      parentId: 'actual-parent-node',
    });
  });

  it('handles missing orderedNumber by using default value', () => {
    const propsWithoutOrderedNumber = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        orderedNumber: undefined,
      },
    };

    renderComponent(propsWithoutOrderedNumber as any);

    // Check that the description uses the default orderedNumber (0)
    expect(screen.getByTestId('description')).toHaveTextContent('0. Default path name');
  });

  it('handles missing data gracefully', () => {
    const propsWithoutData = {
      id: 'test-node',
      data: undefined,
    };

    renderComponent(propsWithoutData as any);

    // The component should render without crashing
    expect(screen.getByTestId('main-path-node')).toBeInTheDocument();
    expect(screen.getByTestId('title')).toHaveTextContent('Path');
    expect(screen.getByTestId('description')).toHaveTextContent('0. Default path name');
  });

  it('handles missing actualParentNodeId gracefully', () => {
    const propsWithoutParentId = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        actualParentNodeId: undefined,
      },
    };

    renderComponent(propsWithoutParentId as any);

    // Find and click the delete button
    const deleteButton = screen.getByTestId('delete-button');
    fireEvent.click(deleteButton);

    // Verify that handleRemoveNode was called with undefined parentId
    expect(mockHandleRemoveNode).toHaveBeenCalledWith({
      nodeId: 'test-node',
      typeRemove: FlowTypeNode.Path,
      parentId: undefined,
    });
  });
});
