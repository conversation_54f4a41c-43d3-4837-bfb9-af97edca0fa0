import { useCallback, useMemo } from 'react';
import BaseSelectNode from '../BaseSelectNode';
import MenuActionNode from '../../MenuActionNode';
import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { FlowTypeNode, type FlowNodeType, FlowNodeType as FlowNodeTypeVal } from '@/models/flow';
import useFlowSchema from '@/hooks/useFlowSchema';
import { useTranslate } from '@tolgee/react';
import { CatalogNodeIcon } from '@resola-ai/ui';

interface SelectTriggerNodeProps {
  id: string;
  data?: {
    orderedNumber?: number;
    actualParentNodeId?: string;
    displayName?: string;
    type?: FlowNodeType;
    [key: string]: any;
  };
  type: FlowNodeType;
}

export default function SelectTriggerNode({ id, data, type }: SelectTriggerNodeProps) {
  const { t } = useTranslate('flow');
  const { schema } = useFlowSchema({ flowNodeType: type });
  const { handleOpenFormPanel, flowActionHandlers } = useFlowBuilderContext();

  const onEdit = useCallback(() => {
    handleOpenFormPanel({
      nodeId: id,
      orderNumber: data?.orderedNumber ?? 0,
      type: data?.type ?? FlowNodeTypeVal.NewTrigger,
      parentNodeId: data?.actualParentNodeId ?? '',
    });
  }, [data, id, handleOpenFormPanel]);

  const onDelete = useCallback(() => {
    flowActionHandlers?.handleRemoveNode({ nodeId: id, typeRemove: FlowTypeNode.TriggerNode });
  }, [flowActionHandlers, id]);

  const orderedNumber = data?.orderedNumber ?? 0;

  const title = useMemo(() => {
    return schema?.displayName;
  }, [schema?.displayName]);

  const displayName = useMemo(() => {
    const defaultName = t('defaultNameActionNode');
    if (data?.displayName === schema?.displayName) {
      return defaultName;
    }
    if (data?.displayName) {
      return data?.displayName;
    }
    return defaultName;
  }, [data?.displayName, schema?.displayName]);

  const description = useMemo(() => {
    return `${orderedNumber}. ${displayName}`;
  }, [orderedNumber, displayName]);

  return (
    <BaseSelectNode
      id={id}
      title={title}
      customIcon={<CatalogNodeIcon name={schema?.name} size={20} />}
      description={description}
      dataTestId='text-trigger'
      openModalAction={onEdit}
      menuContent={<MenuActionNode onDelete={onDelete} onDuplicate={onEdit} />}
    />
  );
}
