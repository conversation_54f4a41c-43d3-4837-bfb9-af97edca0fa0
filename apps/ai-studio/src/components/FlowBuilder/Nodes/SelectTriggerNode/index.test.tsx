import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SelectTriggerNode from './index';
import { FlowBuilderProvider } from '@/contexts/FlowBuilderContext';
import { useTranslate } from '@tolgee/react';
import { FlowNodeType } from '@/models/flow';

const mockHandleOpenFormPanel = vi.fn();
const mockHandleRemoveNode = vi.fn();

// Mock useTranslate
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: vi.fn(),
  };
});

// Mock the FlowBuilderContext
vi.mock('@/contexts/FlowBuilderContext', () => ({
  useFlowBuilderContext: () => ({
    flowActionHandlers: {
      handleRemoveNode: mockHandleRemoveNode,
    },
    handleOpenFormPanel: mockHandleOpenFormPanel,
  }),
  FlowBuilderProvider: ({ children }) => <>{children}</>,
}));

// Mock the useFlowSchema hook
vi.mock('@/hooks/useFlowSchema', () => ({
  __esModule: true,
  default: () => ({
    schema: {
      displayName: 'Select a trigger',
      triggers: {},
      description: 'Select a trigger that starts your flow',
    },
  }),
}));

// Mock the MenuActionNode component
vi.mock('../../MenuActionNode', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({ onDuplicate, onDelete, dataTestId }) => {
    return (
      <div data-testid={dataTestId || 'menu-action-node'}>
        <button
          type='button'
          data-testid='duplicate-button'
          data-action='duplicate'
          onClick={onDuplicate}
        >
          Duplicate
        </button>
        <button type='button' data-testid='delete-button' data-action='delete' onClick={onDelete}>
          Delete
        </button>
      </div>
    );
  }),
}));

// Mock the BaseSelectNode component
vi.mock('../BaseSelectNode', () => ({
  __esModule: true,
  default: vi
    .fn()
    .mockImplementation(({ title, description, dataTestId, menuContent, openModalAction }) => (
      <div data-testid={dataTestId}>
        <div data-testid='title'>{title}</div>
        <div data-testid='description'>{description}</div>
        <button data-testid='node-click-button' onClick={openModalAction}>
          Open Modal
        </button>
        <div data-testid='menu-content'>{menuContent}</div>
      </div>
    )),
}));

describe('SelectTriggerNode', () => {
  const nodeId = 'test-node-id';

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock useTranslate
    (useTranslate as any).mockReturnValue({
      t: (key: string) => {
        const translations = {
          defaultNameActionNode: 'Select an action for your flow',
        };
        return translations[key] || key;
      },
    });
  });

  // Helper function to render the component with the FlowBuilderProvider
  const renderWithProvider = (props) => {
    return render(
      <FlowBuilderProvider>
        <SelectTriggerNode {...props} />
      </FlowBuilderProvider>
    );
  };

  it('should be defined', () => {
    expect(SelectTriggerNode).toBeDefined();
  });

  it('renders with correct props', () => {
    renderWithProvider({ id: nodeId, data: { orderedNumber: 1 } });

    expect(screen.getByTestId('text-trigger')).toBeInTheDocument();
    expect(screen.getByTestId('title')).toHaveTextContent('Select a trigger');
    expect(screen.getByTestId('description')).toHaveTextContent(
      '1. Select an action for your flow'
    );
  });

  it('calls handleOpenFormPanel when node is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: {
        orderedNumber: 1,
        type: FlowNodeType.NewTrigger,
        actualParentNodeId: nodeId,
      },
      type: FlowNodeType.NewTrigger,
    });

    const nodeButton = screen.getByTestId('node-click-button');
    await user.click(nodeButton);

    expect(mockHandleOpenFormPanel).toHaveBeenCalledTimes(1);
    expect(mockHandleOpenFormPanel).toHaveBeenCalledWith({
      nodeId,
      orderNumber: 1,
      parentNodeId: nodeId,
      type: FlowNodeType.NewTrigger,
    });
  });

  it('calls handleOpenFormPanel when duplicate button is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: {
        orderedNumber: 2,
        type: FlowNodeType.NewTrigger,
        actualParentNodeId: nodeId,
      },
      type: FlowNodeType.NewTrigger,
    });

    const duplicateButton = screen.getByTestId('duplicate-button');
    await user.click(duplicateButton);

    expect(mockHandleOpenFormPanel).toHaveBeenCalledTimes(1);
    expect(mockHandleOpenFormPanel).toHaveBeenCalledWith({
      nodeId,
      orderNumber: 2,
      parentNodeId: nodeId,
      type: FlowNodeType.NewTrigger,
    });
  });

  it('calls handleRemoveNode when delete button is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({ id: nodeId });

    const deleteButton = screen.getByTestId('delete-button');
    await user.click(deleteButton);

    expect(mockHandleRemoveNode).toHaveBeenCalledTimes(1);
    expect(mockHandleRemoveNode).toHaveBeenCalledWith({
      nodeId,
      typeRemove: 'trigger-node',
    });
  });

  it('handles missing data gracefully', () => {
    renderWithProvider({ id: nodeId, type: FlowNodeType.NewTrigger });

    expect(screen.getByTestId('text-trigger')).toBeInTheDocument();
    expect(screen.getByTestId('description')).toHaveTextContent(
      '0. Select an action for your flow'
    );
  });

  it('displays correct title and description', () => {
    renderWithProvider({
      id: nodeId,
      data: {
        orderedNumber: 3,
        displayName: 'Custom Trigger Name',
      },
      type: FlowNodeType.NewTrigger,
    });

    expect(screen.getByTestId('title')).toHaveTextContent('Select a trigger');
    expect(screen.getByTestId('description')).toHaveTextContent('3. Custom Trigger Name');
  });
});
