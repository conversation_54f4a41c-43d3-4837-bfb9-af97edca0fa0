import { useMemo, useState } from 'react';
import { Card, Group, rem, Title, Text, ActionIcon, Flex, Box } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconBrandOpenai } from '@tabler/icons-react';
import { useSearchParams } from 'react-router-dom';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import type { Callback, CardStatus } from '@/types';
import { timeAgo } from '@/helpers/timeHelper';
import ActionsMenu, { type ActionsMenuProps } from '../ActionsMenu';
import AlertStatus from '../AlertStatus';

dayjs.extend(relativeTime);

export interface AICardActions extends ActionsMenuProps {
  onTest?: (callback?: Callback<CardStatus>) => void;
  onDelete?: (callback?: Callback<CardStatus>) => void;
  onReconnect?: (callback?: Callback<CardStatus>) => void;
}

interface AICardProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  aiModel?: string;
  actions?: AICardActions;
  flows?: Array<React.ReactNode>;
  isFullWidth?: boolean;
  containerHeight?: string;
  modelIcons?: React.ReactNode;
  switchButton?: React.ReactNode;
  updateTime?: React.ReactNode;
  updatedAt?: Date | string;
  onClick?: () => void;
}

const useStyles = createStyles(
  (theme, { isFullWidth, containerHeight }: { isFullWidth: boolean; containerHeight: string }) => ({
    root: {
      backgroundColor: theme.white,
      borderColor: theme.colors.decaLight[isFullWidth ? 2 : 3],
      borderRadius: isFullWidth ? rem(10) : rem(12),
      height: containerHeight,
      width: '100%',
      minHeight: 'auto',
      gap: rem(8),
      position: 'relative',
      cursor: 'pointer',
      transition: 'border-color 0.2s ease',
      '&:hover': {
        borderColor: theme.colors.decaBlue[6],
      },
    },
    modelIcon: {
      pointerEvents: 'none',
      borderColor: theme.colors.decaLight[2],
    },
    cardModel: {
      borderRight: `1px solid ${theme.colors.decaLight[3]}`,
      paddingRight: rem(10),
    },
    longCardInfo: {
      flex: 4,
      maxWidth: '60%',
    },
    longModelInfo: {
      flex: 2,
    },
    longCardModel: {
      borderRight: 'none',
    },
    modelIconsSwitchGroup: {
      width: 'calc(100% - 35px)',
      position: 'absolute',
      bottom: 10,
    },
    modelIconsSwitchGroupFlex: {
      flex: 2,
    },
    modelIcons: {
      maxWidth: '50%',
      overflowX: 'hidden',
      overflowY: 'hidden',
      flexWrap: 'nowrap',
    },
    title: {
      whiteSpace: 'nowrap',
    },
    description: {
      whiteSpace: 'nowrap',
    },
  })
);

const AICard: React.FC<AICardProps> = ({
  title,
  description,
  icon,
  aiModel,
  actions,
  isFullWidth = false,
  modelIcons,
  switchButton,
  updateTime,
  updatedAt,
  onClick,
  containerHeight = 'auto',
}) => {
  const { classes, cx, theme } = useStyles({ isFullWidth, containerHeight });
  const [searchParams] = useSearchParams();
  const lang = searchParams.get('lang') ?? 'ja';
  const updatedAtString = timeAgo(updatedAt, lang);
  const [status, setStatus] = useState<CardStatus | null>(null);

  const newActions = useMemo(() => {
    if (actions?.onTest) {
      return {
        ...actions,
        onTest: () => {
          actions?.onTest?.(setStatus);
        },
        onDelete: () => {
          actions?.onDelete?.(setStatus);
        },
        onReconnect: () => {
          actions?.onReconnect?.(setStatus);
        },
      };
    }
    return actions;
  }, [actions]);

  const titleElement = useMemo(() => {
    return (
      <Title className={classes.title} order={6} c='decaGrey.9' lineClamp={1}>
        {title}
      </Title>
    );
  }, [title]);

  const modelElement = useMemo(() => {
    return (
      <Group
        data-testid='aic-model-group'
        align='center'
        gap='xs'
        className={cx(
          !!updatedAtString && classes.cardModel,
          isFullWidth && classes.longCardModel
        )}>
        <ActionIcon size={38} variant='outline' radius='xl' className={classes.modelIcon}>
          <IconBrandOpenai data-testid='aic-model-icon' size={22} color={theme.black} />
        </ActionIcon>
        <Text c='decaGrey.5'>{aiModel}</Text>
      </Group>
    );
  }, [aiModel, isFullWidth, updatedAtString]);

  const updatedAtElement = useMemo(() => {
    return (
      <Text c='decaGrey.5' size='sm' mr={rem(100)}>
        {updatedAtString}
      </Text>
    );
  }, [updatedAtString]);

  return !isFullWidth ? (
    <Card withBorder classNames={classes} data-testid='aic-card' onClick={onClick} shadow='none'>
      <Card.Section inheritPadding py='xs' pb={0}>
        <Group justify='space-between' wrap='nowrap'>
          {icon ?? titleElement}
          {actions && <ActionsMenu {...newActions} />}
        </Group>
      </Card.Section>
      {icon && titleElement}
      {description && (
        <Text c='decaGrey.5' lineClamp={2} style={{ whiteSpace: 'pre-line' }}>
          {description}
        </Text>
      )}
      <Flex justify='space-between' align='center' gap={rem(10)}>
        {(aiModel || updatedAt) && (
          <Group align='center' gap={rem(10)}>
            {aiModel && modelElement}
            {updatedAt && updatedAtElement}
          </Group>
        )}
      </Flex>
      {status && (
        <Flex justify='flex-end'>
          <AlertStatus message={status.message} status={status.status} />
        </Flex>
      )}
      {(modelIcons || switchButton) && (
        <Group
          justify='space-between'
          data-testid='aic-model-icons-switch'
          className={classes.modelIconsSwitchGroup}>
          {modelIcons && (
            <Group className={classes.modelIcons} data-testid='aic-model-icons'>
              {modelIcons}
            </Group>
          )}
          {switchButton && (
            <Box data-testid='aic-switch-button' onClick={e => e.stopPropagation()}>
              {switchButton}
            </Box>
          )}
        </Group>
      )}
    </Card>
  ) : (
    <Card
      withBorder
      classNames={classes}
      data-testid='aic-card-full-width'
      onClick={onClick}
      shadow='none'>
      <Flex justify='space-between' align='center' gap={rem(15)} style={{ position: 'relative' }}>
        <Group data-testid='aic-info-group' gap={rem(15)} className={classes.longCardInfo}>
          {icon}
          <Flex direction='column' gap={rem(5)} maw='100%'>
            {titleElement}
            {description && (
              <Text className={classes.description} c='decaGrey.5' lineClamp={1}>
                {description}
              </Text>
            )}
          </Flex>
        </Group>
        {(aiModel || updatedAt) && (
          <Group
            data-testid='aic-model-updated-group'
            justify={aiModel && updatedAt ? 'space-between' : 'flex-start'}
            align='center'
            gap={rem(100)}
            className={classes.longModelInfo}>
            {aiModel && modelElement}
            {updatedAt && updatedAtElement}
          </Group>
        )}
        {status && (
          <AlertStatus
            message={status.message}
            status={status.status}
            dataTestId='aic-status-container'
          />
        )}
        {(modelIcons || switchButton) && (
          <Group
            justify='space-between'
            className={classes.modelIconsSwitchGroupFlex}
            data-testid='aic-model-icons-switch'>
            {modelIcons ? (
              <Group className={classes.modelIcons} data-testid='aic-model-icons'>
                {modelIcons}
              </Group>
            ) : (
              <Box miw={rem(100)} />
            )}
            <Group justify='space-between' align='center' gap={rem(100)}>
              {updateTime}
              {switchButton && (
                <Box data-testid='aic-switch-button' onClick={e => e.stopPropagation()}>
                  {switchButton}
                </Box>
              )}
            </Group>
          </Group>
        )}
        {actions && <ActionsMenu {...newActions} />}
      </Flex>
    </Card>
  );
};

export default AICard;
