import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import IconPrompt from './IconPrompt';
import IconRobotFace from './IconRobotFace';
import IconAgent from './IconAgent';
import IconSlack from './IconSlack';
import IconGoogle from './IconGoogle';

describe('IconPrompt', () => {
  it('renders without crashing', () => {
    const { container } = render(<IconPrompt />);
    expect(container.firstChild).toBeTruthy();
  });

  it('renders with custom props', () => {
    const { container } = render(<IconPrompt className='custom-class' width={40} height={40} />);

    const svg = container.querySelector('svg');
    expect(svg).toBeTruthy();
    expect(svg).toHaveAttribute('class', 'custom-class');
    expect(svg).toHaveAttribute('width', '40');
    expect(svg).toHaveAttribute('height', '40');
  });

  it('maintains default viewBox and xmlns attributes', () => {
    const { container } = render(<IconPrompt />);
    const svg = container.querySelector('svg');

    expect(svg).toHaveAttribute('viewBox', '0 0 23 24');
    expect(svg).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
  });

  it('contains the path element', () => {
    const { container } = render(<IconPrompt />);
    const path = container.querySelector('path');

    expect(path).toBeTruthy();
    expect(path).toHaveAttribute('fill', '#1D2088');
  });
});

describe('IconRobotFace', () => {
  it('renders without crashing', () => {
    const { container } = render(<IconRobotFace />);
    expect(container.firstChild).toBeTruthy();
  });

  it('renders with custom props', () => {
    const { container } = render(
      <IconRobotFace className='custom-class' width={40} height={40} fill='#1D2088' />
    );

    const svg = container.querySelector('svg');
    expect(svg).toBeTruthy();
    expect(svg).toHaveAttribute('class', 'custom-class');
    expect(svg).toHaveAttribute('width', '40');
    expect(svg).toHaveAttribute('height', '40');
    expect(svg).toHaveAttribute('fill', '#1D2088');
  });

  it('maintains default viewBox and xmlns attributes', () => {
    const { container } = render(<IconRobotFace />);
    const svg = container.querySelector('svg');

    expect(svg).toHaveAttribute('viewBox', '0 0 24 21');
    expect(svg).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
  });

  it('contains the path element', () => {
    const { container } = render(<IconRobotFace />);
    const path = container.querySelector('path');

    expect(path).toBeTruthy();
    expect(path).toHaveAttribute('fill', 'currentColor');
  });
});

describe('IconAgent', () => {
  it('renders without crashing', () => {
    const { container } = render(<IconAgent />);
    expect(container.firstChild).toBeTruthy();
  });
});

describe('IconSlack', () => {
  it('renders without crashing', () => {
    const { container } = render(<IconSlack />);
    expect(container.firstChild).toBeTruthy();
  });
});

describe('IconGoogle', () => {
  it('renders without crashing', () => {
    const { container } = render(<IconGoogle />);
    expect(container.firstChild).toBeTruthy();
  });
});
