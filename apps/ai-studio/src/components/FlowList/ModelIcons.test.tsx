import { screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { ModelIcons } from './ModelIcons';
import { describe, it, expect } from 'vitest';
import { mockLibraries, renderWithMantine } from '@/utils/test';

mockLibraries();

describe('ModelIcons', () => {
  it('renders correct number of model icons when flowModels length is greater than 3', () => {
    const flowModels = ['openai', 'code', 'http', 'filter', 'wait'];
    renderWithMantine(
      <MemoryRouter>
        <ModelIcons flowModels={flowModels} />
      </MemoryRouter>
    );

    const modelIcons = screen.getAllByTestId(/aic-model-icon-/);
    expect(modelIcons.length).toBe(3); // 2 model icons + 1 addition number

    expect(screen.getByTestId('aic-model-icon-🤖')).toBeInTheDocument();
    expect(screen.getByTestId('aic-model-icon-🧩')).toBeInTheDocument();
    expect(screen.getByTestId('aic-model-icon-addition-number')).toBeInTheDocument();
    expect(screen.getByText('+3')).toBeInTheDocument();
  });

  it('renders correct model icons when flowModels length is less than or equal to 3', () => {
    const flowModels = ['openai', 'code', 'http'];
    renderWithMantine(
      <MemoryRouter>
        <ModelIcons flowModels={flowModels} />
      </MemoryRouter>
    );

    const modelIcons = screen.getAllByTestId(/aic-few-model-icon-/);
    expect(modelIcons.length).toBe(3);

    expect(screen.getByTestId('aic-few-model-icon-🤖')).toBeInTheDocument();
    expect(screen.getByTestId('aic-few-model-icon-🧩')).toBeInTheDocument();
    expect(screen.getByTestId('aic-few-model-icon-🌐')).toBeInTheDocument();
  });

  it('renders no model icons when flowModels is empty', () => {
    const flowModels: string[] = [];
    renderWithMantine(
      <MemoryRouter>
        <ModelIcons flowModels={flowModels} />
      </MemoryRouter>
    );

    const modelIcons = screen.queryAllByTestId(/aic-model-icon-/);
    const fewModelIcons = screen.queryAllByTestId(/aic-few-model-icon-/);
    const additionNumber = screen.queryByTestId(/aic-model-icon-addition-number/);
    expect(modelIcons.length).toBe(0);
    expect(fewModelIcons.length).toBe(0);
    expect(additionNumber).not.toBeInTheDocument();
  });

  it('renders no model icons when flowModels is null or undefined', () => {
    renderWithMantine(
      <MemoryRouter>
        <ModelIcons flowModels={null as any} />
      </MemoryRouter>
    );

    let modelIcons = screen.queryAllByTestId(/aic-model-icon-/);
    let fewModelIcons = screen.queryAllByTestId(/aic-few-model-icon-/);
    let additionNumber = screen.queryByTestId(/aic-model-icon-addition-number/);
    expect(modelIcons.length).toBe(0);
    expect(fewModelIcons.length).toBe(0);
    expect(additionNumber).not.toBeInTheDocument();

    renderWithMantine(
      <MemoryRouter>
        <ModelIcons flowModels={undefined} />
      </MemoryRouter>
    );

    modelIcons = screen.queryAllByTestId(/aic-model-icon-/);
    fewModelIcons = screen.queryAllByTestId(/aic-few-model-icon-/);
    additionNumber = screen.queryByTestId(/aic-model-icon-addition-number/);
    expect(modelIcons.length).toBe(0);
    expect(fewModelIcons.length).toBe(0);
    expect(additionNumber).not.toBeInTheDocument();
  });

  it('renders correct model icons when flowModels length is 1', () => {
    const flowModels = ['openai'];
    renderWithMantine(
      <MemoryRouter>
        <ModelIcons flowModels={flowModels} />
      </MemoryRouter>
    );

    const modelIcons = screen.getAllByTestId(/aic-few-model-icon-/);
    expect(modelIcons.length).toBe(1);

    expect(screen.getByTestId('aic-few-model-icon-🤖')).toBeInTheDocument();
  });

  it('renders correct model icons when flowModels length is 2', () => {
    const flowModels = ['openai', 'code'];
    renderWithMantine(
      <MemoryRouter>
        <ModelIcons flowModels={flowModels} />
      </MemoryRouter>
    );

    const modelIcons = screen.getAllByTestId(/aic-few-model-icon-/);
    expect(modelIcons.length).toBe(2);

    expect(screen.getByTestId('aic-few-model-icon-🤖')).toBeInTheDocument();
    expect(screen.getByTestId('aic-few-model-icon-🧩')).toBeInTheDocument();
  });
});
