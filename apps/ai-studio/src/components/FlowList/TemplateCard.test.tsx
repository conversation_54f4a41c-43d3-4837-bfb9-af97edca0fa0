import { mockLibraries, renderWithMantine } from '@/utils/test';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import TemplateCard from './TemplateCard';
import userEvent from '@testing-library/user-event';

mockLibraries();

describe('TemplateCard', () => {
  const user = userEvent.setup();
  const mockOnClick = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the TemplateCard component with correct description and model icons when flowModels length is 3', () => {
    const description = 'Test Description';
    const flowModels = ['openai', 'code', 'http'];

    renderWithMantine(
      <TemplateCard description={description} flowModels={flowModels} onClick={mockOnClick} />
    );

    const card = screen.getByTestId('flow-template-card');
    expect(card).toBeInTheDocument();

    const descriptionElement = screen.getByTestId('flow-template-description');
    expect(descriptionElement).toBeInTheDocument();
    expect(descriptionElement.textContent).toBe(description);

    const iconsGroup = screen.getByTestId('flow-template-icons');
    expect(iconsGroup).toBeInTheDocument();

    expect(screen.getByTestId('aic-few-model-icon-🤖')).toBeInTheDocument();
    expect(screen.getByTestId('aic-few-model-icon-🧩')).toBeInTheDocument();
    expect(screen.getByTestId('aic-few-model-icon-🌐')).toBeInTheDocument();
  });

  it('renders the TemplateCard component with addition number when flowModels length is greater than 3', () => {
    const description = 'Test Description';
    const flowModels = ['openai', 'code', 'http', 'filter', 'wait'];

    renderWithMantine(
      <TemplateCard description={description} flowModels={flowModels} onClick={mockOnClick} />
    );

    const card = screen.getByTestId('flow-template-card');
    expect(card).toBeInTheDocument();

    const descriptionElement = screen.getByTestId('flow-template-description');
    expect(descriptionElement).toBeInTheDocument();
    expect(descriptionElement.textContent).toBe(description);

    const iconsGroup = screen.getByTestId('flow-template-icons');
    expect(iconsGroup).toBeInTheDocument();

    const additionNumber = screen.getByTestId('aic-model-icon-addition-number');
    expect(additionNumber).toBeInTheDocument();
    expect(additionNumber.textContent).toBe('+3');
    expect(screen.getByTestId('aic-model-icon-🤖')).toBeInTheDocument();
    expect(screen.getByTestId('aic-model-icon-🧩')).toBeInTheDocument();
  });

  it('renders the TemplateCard component with few model icon when flowModels length is less than 3', () => {
    const description = 'Test Description';
    const flowModels = ['openai', 'code'];

    renderWithMantine(
      <TemplateCard description={description} flowModels={flowModels} onClick={mockOnClick} />
    );

    const card = screen.getByTestId('flow-template-card');
    expect(card).toBeInTheDocument();

    const descriptionElement = screen.getByTestId('flow-template-description');
    expect(descriptionElement).toBeInTheDocument();
    expect(descriptionElement.textContent).toBe(description);

    const iconsGroup = screen.getByTestId('flow-template-icons');
    expect(iconsGroup).toBeInTheDocument();

    expect(screen.getByTestId('aic-few-model-icon-🤖')).toBeInTheDocument();
    expect(screen.getByTestId('aic-few-model-icon-🧩')).toBeInTheDocument();
  });

  it('calls onClick when the card is clicked', async () => {
    const description = 'Test Description';
    const flowModels = ['openai', 'code'];

    renderWithMantine(
      <TemplateCard description={description} flowModels={flowModels} onClick={mockOnClick} />
    );

    const card = screen.getByTestId('flow-template-card');
    await user.click(card);
    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('renders the TemplateCard component with correct description and no model icons when flowModels is empty', () => {
    const description = 'Test Description';
    const flowModels: string[] = [];

    renderWithMantine(
      <TemplateCard description={description} flowModels={flowModels} onClick={mockOnClick} />
    );

    const card = screen.getByTestId('flow-template-card');
    expect(card).toBeInTheDocument();

    const descriptionElement = screen.getByTestId('flow-template-description');
    expect(descriptionElement).toBeInTheDocument();
    expect(descriptionElement.textContent).toBe(description);

    const iconsGroup = screen.getByTestId('flow-template-icons');
    expect(iconsGroup).toBeInTheDocument();
    const modelIcons = screen.queryAllByTestId(/aic-few-model-icon-/);
    const additionNumber = screen.queryByTestId(/aic-model-icon-addition-number/);
    expect(modelIcons.length).toBe(0);
    expect(additionNumber).not.toBeInTheDocument();
  });

  it('renders the TemplateCard component with correct description and no model icons when flowModels is null or undefined', () => {
    const description = 'Test Description';

    renderWithMantine(
      <TemplateCard description={description} flowModels={null as any} onClick={mockOnClick} />
    );

    const card = screen.getByTestId('flow-template-card');
    expect(card).toBeInTheDocument();

    const descriptionElement = screen.getByTestId('flow-template-description');
    expect(descriptionElement).toBeInTheDocument();
    expect(descriptionElement.textContent).toBe(description);

    const iconsGroup = screen.getByTestId('flow-template-icons');
    expect(iconsGroup).toBeInTheDocument();
    let modelIcons = screen.queryAllByTestId(/aic-few-model-icon-/);
    let additionNumber = screen.queryByTestId(/aic-model-icon-addition-number/);
    expect(modelIcons.length).toBe(0);
    expect(additionNumber).not.toBeInTheDocument();

    renderWithMantine(
      <TemplateCard description={description} flowModels={undefined} onClick={mockOnClick} />
    );

    modelIcons = screen.queryAllByTestId(/aic-few-model-icon-/);
    additionNumber = screen.queryByTestId(/aic-model-icon-addition-number/);
    expect(modelIcons.length).toBe(0);
    expect(additionNumber).not.toBeInTheDocument();
  });

  it('renders the TemplateCard component with correct description and model icons when flowModels length is 1', () => {
    const description = 'Test Description';
    const flowModels = ['openai'];

    renderWithMantine(
      <TemplateCard description={description} flowModels={flowModels} onClick={mockOnClick} />
    );

    const card = screen.getByTestId('flow-template-card');
    expect(card).toBeInTheDocument();

    const descriptionElement = screen.getByTestId('flow-template-description');
    expect(descriptionElement).toBeInTheDocument();
    expect(descriptionElement.textContent).toBe(description);

    const iconsGroup = screen.getByTestId('flow-template-icons');
    expect(iconsGroup).toBeInTheDocument();

    const modelIcon = screen.getByTestId(`aic-few-model-icon-🤖`);
    expect(modelIcon).toBeInTheDocument();
  });
});
