import type { Flow } from '@/models/flow';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import FlowTemplateList from './FlowTemplateList';

mockLibraries();

describe('FlowTemplateList', () => {
  const user = userEvent.setup();
  const mockOnSelect = vi.fn();

  const mockFlows: Flow[] = [
    {
      id: '1',
      name: 'Flow 1',
      description: 'Description 1',
      models: ['openai', 'code'],
      updatedAt: new Date().toISOString(),
      status: 'enabled',
      triggers: {},
    },
    {
      id: '2',
      name: 'Flow 2',
      description: 'Description 2',
      models: ['http', 'filter', 'wait', 'pages'],
      updatedAt: new Date().toISOString(),
      status: 'enabled',
      triggers: {},
    },
  ];

  it('renders the FlowTemplateList component with correct number of TemplateCards', () => {
    renderWithMantine(<FlowTemplateList items={mockFlows} onSelect={mockOnSelect} />);

    const templateCards = screen.getAllByTestId('flow-template-card');
    expect(templateCards.length).toBe(mockFlows.length);
  });

  it('renders each TemplateCard with correct description and model icons', () => {
    renderWithMantine(<FlowTemplateList items={mockFlows} onSelect={mockOnSelect} />);

    // Check first flow (2 models - should show both icons)
    const descriptionElement1 = screen.getByText('Description 1');
    expect(descriptionElement1).toBeInTheDocument();
    expect(screen.getByTestId('aic-few-model-icon-🤖')).toBeInTheDocument();
    expect(screen.getByTestId('aic-few-model-icon-🧩')).toBeInTheDocument();

    // Check second flow (4 models - should show 2 icons + addition number)
    const descriptionElement2 = screen.getByText('Description 2');
    expect(descriptionElement2).toBeInTheDocument();
    expect(screen.getByTestId('aic-model-icon-🌐')).toBeInTheDocument();
    expect(screen.getByTestId('aic-model-icon-🔍')).toBeInTheDocument();
    expect(screen.getByTestId('aic-model-icon-addition-number')).toBeInTheDocument();
    expect(screen.getByText('+2')).toBeInTheDocument();
  });

  it('calls onSelect with the correct flow when a TemplateCard is clicked', async () => {
    renderWithMantine(<FlowTemplateList items={mockFlows} onSelect={mockOnSelect} />);

    const templateCards = screen.getAllByTestId('flow-template-card');
    await user.click(templateCards[0]);

    await waitFor(() => {
      expect(mockOnSelect).toHaveBeenCalledTimes(1);
      expect(mockOnSelect).toHaveBeenCalledWith(mockFlows[0]);
    });
  });
});
