import { sendViewEvent } from '@/api/site';
import { useArticles } from '@/hooks';
import { BoxyArticlesElement as BoxyArticlesElementUI } from '@resola-ai/ui/components/PageBuilder';
import { CategoryArticle } from '@resola-ai/ui/types/pageBuilder';

const BoxyArticlesElement = (props: Record<string, any>) => {
  const { displayMode, selectedArticles, maximumArticles } = props;
  const { articles } = useArticles(selectedArticles, displayMode, maximumArticles);
  const _props = { ...props, selectedArticles: articles };
  const handleChangeArticle = (value: string) => {
    const selectedArticle = selectedArticles.find((item: CategoryArticle) => item.value === value);
    if (selectedArticle) {
      sendViewEvent({
        article_id: selectedArticle.id,
        event_type: 'view',
        device_type: 'tablet',
      });
    }
  };
  return <BoxyArticlesElementUI onChangeArticle={handleChangeArticle} {..._props} />;
};

export default BoxyArticlesElement;
