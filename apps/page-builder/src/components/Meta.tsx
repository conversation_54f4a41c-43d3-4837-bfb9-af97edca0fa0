import Head from 'next/head';
interface MetaProps {
  url?: string;
  image: string;
  title: string;
  description: string;
  favicon?: string;
  embedScriptUrl?: string;
}

function Meta({ url = '', image, title, description, favicon, embedScriptUrl }: MetaProps) {
  return (
    <Head>
      <link rel='icon' href={favicon || '/favicon.ico'} />
      <link
        href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+JP&family=Kaisei+Decol&family=Kiwi+Maru&family=LXGW+WenKai+TC&family=M+PLUS+1:wght@100..900&family=M+PLUS+2:wght@100..900&family=Murecho:wght@100..900&family=Noto+Serif+JP:wght@200..900&family=Shippori+Mincho&family=Zen+Maru+Gothic&family=Zen+Old+Mincho&display=swap"
        rel="stylesheet"
      />
      {/* Standard meta tags */}
      <title>{title}</title>
      <meta name='description' content={description} />
      <meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no' />

      {/* Open Graph / Facebook meta tags */}
      <meta property='og:type' content='website' />
      <meta property='og:url' content={url} />
      <meta property='og:title' content={title} />
      <meta property='og:description' content={description} />
      <meta property='og:image' content={image} />

      {/* Twitter meta tags */}
      <meta name='twitter:card' content={image} />
      <meta name='twitter:url' content={url} />
      <meta name='twitter:title' content={title} />
      <meta name='twitter:description' content={description} />
      <meta name='twitter:image' content={image} />

      {embedScriptUrl && <script src={embedScriptUrl} />}
    </Head>
  );
}

export default Meta;
