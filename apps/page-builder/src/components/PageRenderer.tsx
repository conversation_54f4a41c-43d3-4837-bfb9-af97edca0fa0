import Meta from '@/components/Meta';
import { componentResolver } from '@/utils/page-utils';
import { Editor, Frame } from '@craftjs/core';

interface PageRendererProps {
  page: Record<string, any>;
  site?: Record<string, any>;
  siteSetting: Record<string, any>;
  embedScriptUrl: string;
}

export default function PageRenderer({
  page,
  site,
  siteSetting,
  embedScriptUrl,
}: PageRendererProps) {
  if (!page) {
    return null;
  }
  const { theme } = siteSetting;
  return (
    <>
      <Meta
        url=''
        title={`${site?.name ? `${site?.name} - ` : ''}${page.name}`}
        description={page?.metadata?.description || siteSetting?.description || ''}
        image={page?.metadata?.image || site?.visual_assets?.thumbnail || ''}
        favicon={site?.visual_assets?.favicon || siteSetting?.visual_assets?.favicon || ''}
        embedScriptUrl={embedScriptUrl}
      />
      <div
        style={{
          fontFamily: `'${theme?.content?.typography?.body?.font_family || ''}'`,
        }}
      >
        <Editor resolver={componentResolver} enabled={false}>
          <Frame data={page.content}></Frame>
        </Editor>
      </div>
    </>
  );
}
