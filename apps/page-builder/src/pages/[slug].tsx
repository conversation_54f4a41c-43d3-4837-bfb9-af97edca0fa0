import { GetStaticProps, GetStaticPaths } from 'next';
import { getPages } from '@/api/page';
import { processPageData } from '@/utils/page-utils';
import PageRenderer from '@/components/PageRenderer';
import { axiosService } from '@resola-ai/services-shared';
import { getEmbedScriptUrl } from '@/utils/page-utils';

export default function Page({
  page,
  site,
  siteSetting,
  embedScriptUrl,
}: {
  page: Record<string, any>;
  site?: Record<string, any>;
  siteSetting: Record<string, any>;
  embedScriptUrl: string;
}) {
  return (
    <PageRenderer
      page={page}
      site={site}
      siteSetting={siteSetting}
      embedScriptUrl={embedScriptUrl}
    />
  );
}

export const getStaticPaths: GetStaticPaths = async () => {
  try {
    if (process.env.ENABLE_STATIC_EXPORT !== 'true') {
      return {
        paths: [],
        fallback: false,
      };
    }
    const siteId = process.env.BUILD_SITE_ID as string;
    axiosService.setOrgId(process.env.NEXT_PUBLIC_ORG_ID as string);
    if (!siteId) {
      throw new Error('BUILD_SITE_ID environment variable is not defined');
    }
    const pages = await getPages(siteId);
    const paths = pages.map(page => ({
      params: { slug: page.url },
    }));

    return {
      paths,
      fallback: false,
    };
  } catch (error) {
    console.error('Error in getStaticPaths:', error);
    return {
      paths: [],
      fallback: false,
    };
  }
};

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const siteId = process.env.BUILD_SITE_ID as string;
  if (!params?.slug) return { notFound: true };
  
  try {
    const [result, embedScriptUrl] = await Promise.all([
      processPageData(siteId, params.slug as string),
      getEmbedScriptUrl(siteId, params.slug as string),
    ]);

    if (!result) return { notFound: true, props: {} };

    return {
      props: {
        ...result,
        embedScriptUrl,
      },
    };
  } catch (e) {
    console.error('Error in getStaticProps:', e);
    return { notFound: true, props: {} };
  }
};
