import { GetServerSideProps } from 'next';
import { axiosService } from '@resola-ai/services-shared';
import { getEmbedScriptUrl, processPageData } from '@/utils/page-utils';
import PageRenderer from '@/components/PageRenderer';

const isStaticExport = process.env.ENABLE_STATIC_EXPORT === 'true';

export default function PreviewPage({
  page,
  site,
  siteSetting,
  embedScriptUrl,
}: {
  page: Record<string, any>;
  site?: Record<string, any>;
  siteSetting: Record<string, any>;
  embedScriptUrl: string;
}) {
  return (
    <PageRenderer
      page={page}
      site={site}
      siteSetting={siteSetting}
      embedScriptUrl={embedScriptUrl}
    />
  );
}

export const getServerSideProps: GetServerSideProps | undefined = !isStaticExport
  ? async ({ params, req }) => {
      if (!params?.slug) return { notFound: true };

      const siteId = req.cookies['preview_site_id'];
      const accessToken = req.cookies['preview_access_token'];
      const orgId = req.cookies['preview_org_id'];

      if (!siteId) {
        return {
          notFound: true,
          props: {
            error: 'Site ID is required for preview mode. Please set preview_site_id cookie',
          },
        };
      }

      if (!accessToken) {
        return {
          notFound: true,
          props: {
            error:
              'Access token is required for preview mode. Please set preview_access_token cookie',
          },
        };
      }

      if (!orgId) {
        return {
          notFound: true,
          props: {
            error: 'Org ID is required for preview mode. Please set preview_org_id cookie',
          },
        };
      }

      try {
        axiosService.setAccessToken(accessToken);
        axiosService.setOrgId(orgId as string);
        const [result, embedScriptUrl] = await Promise.all([
          processPageData(siteId, params.slug as string),
          getEmbedScriptUrl(siteId, params.slug as string),
        ]);

        if (!result) return { notFound: true, props: {} };

        return {
          props: {
            ...result,
            isPreview: true,
            embedScriptUrl,
          },
        };
      } catch (e) {
        console.error('Error in getServerSideProps:', e);
        return {
          notFound: true,
          props: {
            error: 'Failed to load preview page',
          },
        };
      }
    }
  : undefined;
