import NextDocument, { Head, Html, Main, NextScript, DocumentContext } from 'next/document';
import createEmotionServer from '@emotion/server/create-instance';
import { ColorSchemeScript } from '@mantine/core';
import { createGetInitialProps } from '@mantine/emotion';
import { emotionCache } from '@/cache';
import { getSiteSetting } from '@/api/site';

type CustomProps = {
  headerCode?: string;
  footerCode?: string;
  embedScriptUrl?: string;
};

export default function Document(props: CustomProps) {
  const { headerCode = '', footerCode = '', embedScriptUrl = '' } = props;

  return (
    <Html lang='en' suppressHydrationWarning>
      <Head>
        <ColorSchemeScript defaultColorScheme='auto' />
        <link
          href='https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap'
          rel='stylesheet'
        />
        <div dangerouslySetInnerHTML={{ __html: headerCode }} />
        {embedScriptUrl && <script src={embedScriptUrl} async defer />}
      </Head>
      <body>
        <Main />
        <NextScript />
        <div dangerouslySetInnerHTML={{ __html: footerCode }} />
      </body>
    </Html>
  );
}

const stylesServer = createEmotionServer(emotionCache);

Document.getInitialProps = async (ctx: DocumentContext) => {
  const initialProps = await createGetInitialProps(NextDocument, stylesServer)(ctx);
  const siteId = process.env.BUILD_SITE_ID as string;

  try {
    const siteSetting = await getSiteSetting(siteId);
    return {
      ...initialProps,
      headerCode: siteSetting?.html?.header || '',
      footerCode: siteSetting?.html?.footer || '',
    };
  } catch (error) {
    return {
      ...initialProps,
      headerCode: '',
      footerCode: '',
    };
  }
};
