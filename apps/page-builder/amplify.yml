version: 1
applications:
  - appRoot: apps/page-builder
    frontend:
      phases:
        preBuild:
          commands:
            - nvm install 20.19.0 && nvm use 20.19.0
            - npm install -g pnpm@9.15.5
            - pnpm install --no-frozen-lockfile --node-linker=hoisted
        build:
          commands:
            - env | grep -e NEXT_ > apps/page-builder/.env  && cat apps/page-builder/.env
            - bash apps/page-builder/build.sh ssr
            - ls apps/page-builder/
      artifacts:
        baseDirectory: apps/page-builder/.next
        files:
          - '**/*'
      buildPath: /
      # cache:
      #   paths:
      #     - node_modules/**/*
