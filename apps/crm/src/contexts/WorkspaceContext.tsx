import AppConfig from '@/configs';
import { ColumnWidth, UN_ADDROW_FIELDS } from '@/constants/workspace';
import { useAppContext } from '@/contexts/AppContext';
import { useData, useObject, useRecordsCount, useTags, useView, useViews } from '@/hooks';
import type { Record as ProfileRecord } from '@/models';
import { FieldAPI, ObjectAPI, RecordAPI, ViewAPI, WorkspaceAPI } from '@/services/api';
import {
  createNewRow,
  findViewInList,
  getSelectedRowDetails,
  handleActionRow,
  normalizeViewGroups,
  sortPinnedRecords,
  isFieldInFiltersOrSorts,
  useShowRecordUpdateNotification,
} from '@/utils';
import { useDisclosure } from '@mantine/hooks';
import {
  FieldTypes,
  type GroupItem,
  type ObjectSettings,
  type OnViewChange,
  type View,
  type ViewGroup,
  ViewType,
} from '@resola-ai/ui/components';
import {
  DefaultTableToolbarChangeTypes,
  TableCustomFieldsChangeTypes,
  TableFilterChangeTypes,
  TableSelectViewChangeTypes,
  TableSortChangeTypes,
} from '@resola-ai/ui/components/DecaTable/components/Toolbar';
import { TableMenuViewChangeTypes } from '@resola-ai/ui/components/DecaTable/constants';
import { showCustomNotification } from '@resola-ai/ui/components/DecaTable/utils';
import { PERMISSION_KEYS, isPermissionAllowed } from '@resola-ai/ui/components/DecaTable/utils';
import { usePathParams } from '@resola-ai/ui/hooks';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

const useWorkspace = () => {
  const [profileRecord, setProfileRecord] = useState<ProfileRecord>();
  const [validationErrors, setValidationErrors] = useState<Record<string, string | undefined>>({});
  const { wsId, id: objId, recordId, viewId } = useParams();
  const [opened, { open, close }] = useDisclosure(false);
  const [rowSelection, setRowSelection] = useState<any>({});
  const [pinnedRecords, setPinnedRecords] = useState<any[]>([]);
  const { object, mutate: mutateObject, objectLoading } = useObject(wsId, objId);
  const { tags, mutate: mutateTags } = useTags(wsId, objId);
  const [viewApiLoading, setViewApiLoading] = useState(false);
  const { reloadObject, mutateObjects } = useAppContext();
  const navigate = useNavigate();
  const { createPathWithLngParam } = usePathParams();
  const [textSearch, setTextSearch] = useState('');
  const { t } = useTranslate('workspace');
  const showRecordUpdateNotification = useShowRecordUpdateNotification();

  const MAIN_URL = useMemo(
    () => `${AppConfig.BASE_PATH}workspace/${wsId}/objects/${objId}`,
    [wsId, objId]
  );
  const {
    views: viewsData,
    isLoading: viewsLoading,
    mutate: mutateViews,
  } = useViews(wsId || '', objId || '');
  const { recordsCount } = useRecordsCount(
    wsId || '',
    objId || '',
    object?.enableRecordCounter || false
  );
  const columns = useMemo(() => object?.fields || [], [object?.fields]);

  const views = useMemo(() => {
    return (
      viewsData?.map((view) => ({
        ...view,
        isDefault: view.id === object?.userconfig?.viewId,
        recordsCount: recordsCount?.values?.[view.id] || 0,
        lastUpdated: recordsCount?.datetime || '',
      })) || []
    );
  }, [viewsData, object?.userconfig?.viewId, recordsCount]);

  const objectSettings: ObjectSettings = useMemo(() => {
    // Add more object settings here when pass to DecaTable
    return {
      enableRecordCounter: object?.enableRecordCounter || false,
    };
  }, [object?.enableRecordCounter]);

  // --- Deriving activeView using useMemo ---
  const getActiveView = useMemo((): View | undefined => {
    if (!views.length || !objId) {
      return undefined;
    }

    // 1. Try viewId from URL
    const viewFromUrl = findViewInList(views, viewId);
    if (viewFromUrl) {
      return viewFromUrl;
    }

    // 2. Try viewId from object.userconfig
    if (object?.userconfig?.viewId) {
      const savedView = findViewInList(views, object.userconfig.viewId);
      if (savedView) {
        return savedView;
      }
    }

    return undefined;
  }, [views, objId, viewId, object?.userconfig?.viewId]);

  const { view: activeView, mutateView } = useView(
    wsId || '',
    objId || '',
    viewId || getActiveView?.id || views[0]?.id || ''
  );

  // --- Modified redirectToView to handle view navigation ---
  const redirectToView = useCallback(
    (view: View, recordId?: string) => {
      if (!view || !objId) return;

      const viewPath = `${MAIN_URL}/views/${view.id}`;
      const recordPath = recordId ? `/${recordId}` : '';
      const fullPath = createPathWithLngParam(viewPath + recordPath);
      navigate(fullPath, { replace: true });
      setTextSearch('');
    },
    [createPathWithLngParam, navigate, MAIN_URL, objId]
  );

  // --- Effect to sync URL if needed ---
  useEffect(() => {
    if (!viewId && activeView && !recordId) {
      const targetPath = createPathWithLngParam(`${MAIN_URL}/views/${activeView.id}`);
      if (window.location.pathname !== targetPath) {
        redirectToView(activeView);
      }
    }
  }, [viewId, activeView, recordId, redirectToView, createPathWithLngParam, MAIN_URL]);

  // --- useData hook now depends on the memoized activeView ---
  const {
    records: rawData,
    isLoading: recordsLoading,
    mutate: mutateRecord,
    size,
    setSize,
    totalRecords,
    isValidating: recordsValidating,
  } = useData(wsId, activeView?.id, textSearch ?? '', 70, activeView);

  // Apply pinned records sorting to the data
  const data = useMemo(() => {
    return sortPinnedRecords(rawData, pinnedRecords);
  }, [rawData, pinnedRecords]);

  useEffect(() => {
    objId && setRowSelection({});
  }, [objId]);

  useEffect(() => {
    if (reloadObject === objId) {
      mutateObject();
    }
  }, [reloadObject, objId]);

  const updateFields = useCallback(
    async (field, fieldId, pos?, viewId?) => {
      const position = pos !== undefined ? pos : field.options?.moveToFirst ? 0 : undefined;

      if (fieldId) {
        await FieldAPI.update(wsId || '', objId || '', fieldId, { field, position });
      } else {
        await FieldAPI.save(wsId || '', objId || '', { field, position, viewId });
      }

      if (object) {
        await mutateObject();
        if (
          [FieldTypes.RELATIONSHIP, FieldTypes.CURRENCY, FieldTypes.PERCENT].includes(field.type)
        ) {
          await mutateRecord();
        }
      }
    },
    [wsId, objId, object, mutateObject, mutateRecord]
  );

  const handleAddColumn = useCallback(
    async (formData) => {
      const newField = {
        name: formData.name,
        type: formData.type,
        options: formData.options,
        isProtected: formData.isProtected || false,
      };

      let pos;
      let viewIdForNewField: string | undefined = undefined;
      if (formData?.id && activeView?.fieldOrder) {
        // Check activeView exists
        const index = activeView.fieldOrder.findIndex((id) => id === formData.id);
        if (index !== -1) {
          pos = formData.insertType === 'left' ? index : index + 1;
          viewIdForNewField = activeView.id; // Associate with the current view if inserting relative
        }
      }

      await updateFields(newField, '', pos, viewIdForNewField);
      await mutateView();

      if (!object?.fields?.length) {
        const newRow = createNewRow(columns);
        await RecordAPI.save(wsId || '', objId || '', newRow);
      }
      // if created time or modified time, should mutate the new records
      if (UN_ADDROW_FIELDS.includes(newField.type)) {
        await mutateRecord();
      }
    },
    [activeView, object?.fields?.length, updateFields, mutateRecord]
  );

  const handleUpdateColumn = useCallback(
    async (colId, formData) => {
      const field = object?.fields?.find((col) => col.id === colId);
      if (!field) return;
      const _updatedField = {
        ...field,
        name: formData.name || field.name,
        options: formData.options,
        isProtected: formData.isProtected ?? field.isProtected,
      };

      await updateFields(_updatedField, colId);
      await mutateView();
    },
    [object?.fields, updateFields, mutateView]
  );

  const onSaveData = useCallback(
    async (value, rowIndex, colId, revalidate?: boolean) => {
      const currRecord = data?.[rowIndex];
      const recordId = currRecord?.id;
      const _value = typeof value === 'string' ? value.trim() : value;

      // Find the field definition to check its type
      const fieldDef = columns.find((col) => col.id === colId);
      const isRelationshipField = fieldDef?.type === FieldTypes.RELATIONSHIP;
      const isDateRangeField =
        fieldDef?.type === FieldTypes.DATETIME && fieldDef?.options?.dateRange;

      // Always call the API first to persist the data and get the actual response
      try {
        // Prepare the payload based on field type
        const payload: any =
          isDateRangeField && Array.isArray(_value) && _value.length === 2
            ? {
                id: recordId,
                [colId]: _value[0],
                [`${colId}_end`]: _value[1],
              }
            : {
                id: recordId,
                [colId]: _value,
              };

        const updatedRecord = await RecordAPI.updateByField(
          wsId || '',
          objId || '',
          recordId,
          colId,
          payload
        );

        // Handle auto-sort case
        if (activeView?.autoSort && activeView?.sort?.some((item) => item.fieldId === colId)) {
          await ViewAPI.update(wsId || '', objId || '', activeView.id, activeView);
        }

        // Check if the updated field is involved in filters or sorts
        const isFieldInFiltersOrSortsActive = isFieldInFiltersOrSorts(colId, activeView);

        if (revalidate) {
          mutateRecord(); // Revalidate from server
        } else {
          // Extract field value from API response (handles nested record structure)
          const fieldValueFromResponse = updatedRecord?.[colId] || updatedRecord?.record?.[colId];

          // For relationship fields, prioritize the server response over the sent value
          // as the server response contains the complete relationship structure
          let finalUpdatedRecord: any;

          if (isRelationshipField && fieldValueFromResponse) {
            finalUpdatedRecord = {
              ...currRecord,
              // Merge any response data (both top-level and nested)
              ...(updatedRecord || {}),
              ...(updatedRecord?.record || {}),
              // Ensure the field value is properly set from response
              [colId]: fieldValueFromResponse,
            };
          } else if (isDateRangeField && Array.isArray(_value) && _value.length === 2) {
            // For date range fields, update both start and end fields
            finalUpdatedRecord = {
              ...currRecord,
              [colId]: _value[0],
              [`${colId}_end`]: _value[1],
              // Merge any response data (both top-level and nested)
              ...(updatedRecord || {}),
              ...(updatedRecord?.record || {}),
            };
          } else {
            finalUpdatedRecord = {
              ...currRecord,
              [colId]: _value,
              // Merge any response data (both top-level and nested)
              ...(updatedRecord || {}),
              ...(updatedRecord?.record || {}),
            };
          }

          // Check if the record is currently pinned
          const isRecordPinned = pinnedRecords.some((record) => record.id === recordId);

          // If record is pinned OR (has filters/sorts AND field affects visibility),
          // use pinned records approach
          if (isRecordPinned || isFieldInFiltersOrSortsActive) {
            setPinnedRecords((prev) => {
              // Check if record is already pinned
              const existingIndex = prev.findIndex((record) => record.id === recordId);
              if (existingIndex !== -1) {
                // Update existing pinned record
                const updatedPinned = [...prev];
                updatedPinned[existingIndex] = finalUpdatedRecord;
                return updatedPinned;
              } else {
                // Add new pinned record
                return [finalUpdatedRecord, ...prev];
              }
            });

            // Show notification for filters/sorts (only if field is involved in filters/sorts)
            if (isFieldInFiltersOrSortsActive) {
              setTimeout(() => {
                showRecordUpdateNotification({
                  callBack: async () => {
                    await mutateRecord();
                    setPinnedRecords([]);
                  },
                });
              }, 3000);
            }
          } else {
            // Normal case: update local cache directly
            mutateRecord(
              (currData) => {
                if (!currData || !currData.length) return currData;

                // Find which page contains the record we're updating
                let cumulativeIndex = 0;
                const updatedPages = currData.map((page) => {
                  const pageRecords = page.records || [];
                  const pageStartIndex = cumulativeIndex;
                  const pageEndIndex = cumulativeIndex + pageRecords.length;

                  if (rowIndex >= pageStartIndex && rowIndex < pageEndIndex) {
                    // This page contains the record we need to update
                    const localIndex = rowIndex - pageStartIndex;
                    const updatedRecords = pageRecords.map((record, index) =>
                      index === localIndex ? finalUpdatedRecord : record
                    );

                    cumulativeIndex += pageRecords.length;
                    return {
                      ...page,
                      records: updatedRecords,
                    };
                  }
                  cumulativeIndex += pageRecords.length;
                  return page;
                });

                return updatedPages;
              },
              {
                revalidate: false, // Don't revalidate from server since we're using API response
              }
            );
          }
        }
      } catch (error) {
        console.error('Failed to save data:', error);
      }
    },
    [
      data,
      wsId,
      objId,
      activeView?.autoSort,
      mutateRecord,
      columns,
      setPinnedRecords,
      pinnedRecords,
    ]
  );

  const onSavingCell = async (value, cell) => {
    onSaveData(value, cell.row.index, cell.column.id, false);
  };

  const deleteRow = useCallback(
    async (rowIds: string[]) => {
      try {
        await Promise.all(rowIds.map((id) => RecordAPI.delete(wsId || '', objId || '', id)));
        await mutateRecord();
        setRowSelection({});
      } catch (error) {
        console.error('Failed to delete rows:', error);
      }
    },
    [wsId, objId, mutateRecord]
  );

  const handleActionRowWrapper = useCallback(
    (action: string, rowIds: string[]) => {
      return handleActionRow(action, rowIds, deleteRow, async () => {}); // TODO: waiting for new feature to update
    },
    [deleteRow]
  );

  const handleDeleteCol = useCallback(
    async (id: string) => {
      if (object?.id && wsId) {
        try {
          await FieldAPI.delete(wsId, object.id, id);
          await mutateObject();
          await mutateRecord();
        } catch (error) {
          console.error('Failed to delete column:', error);
        }
      }
    },
    [object?.id, wsId, mutateObject, mutateRecord]
  );

  const openProfile = useCallback(
    (recordId: string, record?: any) => {
      setProfileRecord(record);
      navigate(createPathWithLngParam(`${MAIN_URL}/views/${activeView?.id}/${recordId}`));
      open();
    },
    [setProfileRecord, navigate, createPathWithLngParam, MAIN_URL, activeView, open]
  );

  const handleViewChange = useCallback<OnViewChange<string>>(
    async (id, view, type) => {
      if (!objId || !wsId || !object) {
        console.error('Missing context for view change:', { objId, wsId, object });
        return;
      }

      setViewApiLoading(true);
      try {
        switch (type) {
          case TableMenuViewChangeTypes.CREATE_VIEW: {
            const fields =
              object.fields?.map((field) => ({
                fieldMetaId: field.id,
                isVisible: true,
              })) || [];
            const newViewData = { ...view, fields };
            const newView = await ViewAPI.save(wsId, objId, newViewData);
            await mutateObject();
            await mutateViews();
            redirectToView(newView as unknown as View);
            break;
          }
          case TableMenuViewChangeTypes.SWITCH_VIEW: {
            redirectToView(view);
            break;
          }
          case TableSelectViewChangeTypes.DUPLICATE_VIEW: {
            const fields =
              view.fields
                ?.map((field: any) => ({
                  fieldMetaId: field.fieldMetaId || field.id,
                  isVisible: field.isVisible ?? true,
                  size: ColumnWidth[field.type],
                }))
                .filter((f) => f.fieldMetaId) || [];

            const duplicatedViewData = {
              ...view,
              fields: fields,
              isLocked: false,
            };
            const newView = await ViewAPI.save(wsId, objId, duplicatedViewData);
            if (newView) {
              await mutateObject();
              await mutateViews();
              showCustomNotification(t('viewDuplicated', { defaultValue: '', name: newView.name }));
              redirectToView(newView as unknown as View);
            }
            break;
          }
          case TableSelectViewChangeTypes.DELETE_VIEW: {
            const viewPos = views.findIndex((v) => v.id === id);
            let nextView: View | undefined;
            if (views.length <= 1) {
              nextView = undefined;
            } else if (viewPos === 0) {
              nextView = views[1];
            } else {
              nextView = views[viewPos > 0 ? viewPos - 1 : 0];
            }

            await ViewAPI.delete(wsId || '', objId || '', id);
            await mutateObject();
            await mutateViews();
            if (nextView) redirectToView(nextView);
            break;
          }
          case TableSelectViewChangeTypes.LOCK_VIEW: {
            const updatedView = await ViewAPI.update(wsId || '', objId || '', id, view);
            if (updatedView) {
              showCustomNotification(
                t(updatedView?.locked ? 'viewUnlocked' : 'viewLocked', {
                  name: updatedView?.name,
                })
              );
              await mutateView(updatedView, false);
              await mutateObject((currentObject) => {
                if (!currentObject) return currentObject;
                const updatedViews = currentObject.views?.map((v) =>
                  v.id === view.id ? updatedView : v
                );
                return { ...currentObject, views: updatedViews };
              }, false);
              await mutateViews();
              await mutateObjects();
            }
            break;
          }
          case TableCustomFieldsChangeTypes.EDIT_COLUMN: {
            const fieldData = view.fields?.find((f) => f.fieldMetaId === id || f.id === id);
            if (fieldData) {
              await handleUpdateColumn(id, fieldData);
            } else {
              console.error('Field data not found for editing column:', id);
            }
            break;
          }
          case TableCustomFieldsChangeTypes.DUPLICATE_COLUMN: {
            const col = object.fields?.find((c) => c.id === id);
            if (col) {
              await handleAddColumn({
                ...col,
                name: `${col.name} copy`,
                options: { ...col.options },
                isProtected: false,
              });
            } else {
              console.error('Field definition not found for duplicating column:', id);
            }
            break;
          }
          case TableCustomFieldsChangeTypes.DELETE_COLUMN: {
            await handleDeleteCol(id);
            break;
          }
          case TableFilterChangeTypes.FILTER_VIEW:
          case TableSortChangeTypes.FILTER_VIEW: {
            const updatedView = await ViewAPI.update(wsId || '', objId || '', view.id, view);
            await mutateView(updatedView, false);
            await mutateObject((currentObject) => {
              if (!currentObject) return currentObject;
              const updatedViews = currentObject.views?.map((v) =>
                v.id === view.id ? updatedView : v
              );
              return { ...currentObject, views: updatedViews };
            }, false);
            await mutateRecord();
            break;
          }
          case DefaultTableToolbarChangeTypes.UPDATE_VIEW: // Catch-all for general view property updates
          default: {
            // This could be column resize, reorder, visibility change etc., passed in 'view'
            const updatedView = await ViewAPI.update(wsId || '', objId || '', id, view);
            if (updatedView) {
              await mutateView(updatedView, false);
              await mutateObject();
              await mutateViews();
            }
            type === TableSelectViewChangeTypes.CLEAR_VIEW && mutateRecord();
            break;
          }
        }
      } catch (error) {
        console.error('Error handling view change:', type, error);
        await mutateObject();
        await mutateView();
        // Only mutate records for operations that affect record display
        if (
          type === TableFilterChangeTypes.FILTER_VIEW ||
          type === TableSortChangeTypes.FILTER_VIEW ||
          type === TableSelectViewChangeTypes.CLEAR_VIEW
        ) {
          await mutateRecord();
        }
      } finally {
        setViewApiLoading(false);
      }
    },
    [objId, wsId, object, views, redirectToView]
  );

  const handleApplyManageView = useCallback(
    async (viewGroups: ViewGroup[], defaultView: string, enableRecordCounter?: boolean) => {
      const { views: updatedViews, viewGroups: updatedViewGroups } = normalizeViewGroups(
        viewGroups,
        views
      );

      await Promise.all(
        (updatedViews ?? []).map((view) => ViewAPI.update(wsId || '', objId || '', view.id, view))
      );

      // Update the object with viewGroups and enableRecordCounter, and set userConfig.viewId
      await ObjectAPI.update(wsId || '', {
        ...object,
        viewGroups: updatedViewGroups,
        ...(enableRecordCounter !== undefined && { enableRecordCounter }),
      });
      await WorkspaceAPI.updateUserConfig(wsId || '', {
        viewId: defaultView,
        objectId: objId || '',
      });
      await mutateObject();
    },
    [object, wsId, redirectToView, mutateObject]
  );

  const currRecordIndex = useMemo(() => {
    return data?.findIndex((record) => record.id === profileRecord?.id);
  }, [data, profileRecord]);

  useEffect(() => {
    if (recordId && data.length) {
      const record = data.find((record) => record.id === recordId);
      setProfileRecord(record);
      open();
    }
  }, [recordId, data]);

  const selectedRowDetails = useMemo(() => {
    return getSelectedRowDetails(rowSelection, data);
  }, [rowSelection, data]);

  const viewGroups = useMemo(() => {
    return object?.viewGroups
      ?.map((group) => {
        if (group.type === ViewType.GROUP) {
          return {
            ...group,
            views: (group as GroupItem).viewIds
              ?.map((id) => views?.find((v) => v.id === id))
              .filter(Boolean),
          };
        }
        return views?.find((v) => v.id === group.id)
          ? { ...group, view: { ...views?.find((v) => v.id === group.id) } }
          : null;
      })
      .filter((group) => {
        if (
          group?.type === ViewType.GROUP &&
          group?.views?.length === 0 &&
          !isPermissionAllowed(object?.permission || {}, PERMISSION_KEYS.OBJECT_UPDATE)
        ) {
          return false;
        }
        return true;
      })
      .filter(Boolean);
  }, [object, views]);

  const handleSearch = useCallback((searchQuery: string) => {
    setTextSearch(searchQuery);
  }, []);

  return {
    data,
    columns,
    handleAddColumn,
    validationErrors,
    setValidationErrors,
    onSavingCell,
    handleUpdateColumn,
    opened,
    open,
    close,
    views,
    activeView,
    handleViewChange,
    loading: objectLoading,
    handleActionRow: handleActionRowWrapper,
    openProfile,
    object,
    profileRecord,
    currRecordIndex,
    onSaveData,
    refetchObject: mutateObject,
    viewLoading: viewApiLoading || viewsLoading,
    recordsLoading: recordsLoading || recordsValidating,
    rowSelection,
    setRowSelection,
    selectedRowDetails,
    mutateRecord,
    mutateView,
    tags,
    mutateTags,
    size,
    setSize,
    totalRecords,
    mutateObject,
    viewGroups,
    handleApplyManageView,
    textSearch,
    handleSearch,
    objectSettings,
    setPinnedRecords,
  };
};

export type TableContextType = ReturnType<typeof useWorkspace>;

const context = createContext<TableContextType | null>(null);

export const WorkspaceContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useWorkspace();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useWorkspaceContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useWorkspaceContext must be used inside WorkspaceContextProvider');
  }

  return value;
};
