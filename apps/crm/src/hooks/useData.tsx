import { RecordAPI } from '@/services/api';
import React, { useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import useSWRInfinite from 'swr/infinite';

export const useData = (
  wsId,
  viewId,
  textSearch?: string | null,
  defaultLimit = 70,
  viewConfig?: any
) => {
  const [searchParams] = useSearchParams();
  const pageSizeParam = searchParams.get('pageSize');
  const limit = pageSizeParam ? Number.parseInt(pageSizeParam, 10) : defaultLimit;

  const getKey = useCallback(
    (pageIndex: number, previousPageData: any) => {
      if (!wsId || !viewId) return null;

      // reached the end
      if (previousPageData && !previousPageData.records?.length) return null;

      // Include view configuration in cache key so <PERSON><PERSON> knows when filters/sorts change
      const filtersHash = viewConfig?.filters ? JSON.stringify(viewConfig.filters) : '';
      const sortsHash = viewConfig?.sort ? JSON.stringify(viewConfig.sort) : '';

      return [wsId, viewId, pageIndex * limit, limit, textSearch ?? '', filtersHash, sortsHash];
    },
    [wsId, viewId, limit, textSearch, viewConfig]
  );

  const { data, isLoading, isValidating, mutate, size, setSize } = useSWRInfinite(
    getKey,
    ([wsId, viewId, offset, limit, textSearch]) => {
      return RecordAPI.get(wsId, viewId, textSearch, offset, limit).then((res) => {
        return {
          records: res?.records,
          totalRecordsCount: res?.totalRecordsCount,
        };
      });
    },
    {
      revalidateIfStale: true,
      keepPreviousData: true,
      dedupingInterval: 2000,
      revalidateFirstPage: false,
      revalidateOnFocus: false,
    }
  );

  return React.useMemo(() => {
    const records = data?.flatMap((d) => d?.records || []) || [];
    return {
      records,
      isLoading,
      isValidating,
      mutate,
      size,
      setSize,
      totalRecords: data?.[0]?.totalRecordsCount || 0,
    };
  }, [data, size, isLoading, isValidating, mutate]);
};
