import { FieldTypes } from '@resola-ai/ui/components';
import { beforeEach, describe, expect, it } from 'vitest';
import {
  calculateTotalRecords,
  createFieldPayload,
  createFieldsToConfirmMap,
  createInitialFieldMappings,
  filterAutoCreatedFields,
  getActiveFieldMappings,
  getAutoMatchedFieldType,
  getCsvFields,
  getFieldMappingByField,
  getSkippedFieldMappings,
  matchCsvFieldsToObjectFields,
  normalizeFieldMappings,
  updateFieldMapping,
  validateFieldMappings,
  type FieldMapping,
} from './csvImportUtils';

describe('csvImportUtils', () => {
  describe('getAutoMatchedFieldType', () => {
    it('should match email field types correctly', () => {
      expect(getAutoMatchedFieldType('email')).toBe(FieldTypes.EMAIL);
      expect(getAutoMatchedFieldType('Email Address')).toBe(FieldTypes.EMAIL);
      expect(getAutoMatchedFieldType('user_email')).toBe(FieldTypes.EMAIL);
      expect(getAutoMatchedFieldType('mail')).toBe(FieldTypes.EMAIL);
      expect(getAutoMatchedFieldType('CONTACT_EMAIL')).toBe(FieldTypes.EMAIL);
    });

    it('should match phone field types correctly', () => {
      expect(getAutoMatchedFieldType('phone')).toBe(FieldTypes.PHONE_NUMBER);
      expect(getAutoMatchedFieldType('Phone Number')).toBe(FieldTypes.PHONE_NUMBER);
      expect(getAutoMatchedFieldType('tel')).toBe(FieldTypes.PHONE_NUMBER);
      expect(getAutoMatchedFieldType('mobile')).toBe(FieldTypes.PHONE_NUMBER);
      expect(getAutoMatchedFieldType('cellular')).toBe(FieldTypes.PHONE_NUMBER);
      expect(getAutoMatchedFieldType('CONTACT_PHONE')).toBe(FieldTypes.PHONE_NUMBER);
    });

    it('should match URL field types correctly', () => {
      expect(getAutoMatchedFieldType('url')).toBe(FieldTypes.URL);
      expect(getAutoMatchedFieldType('website')).toBe(FieldTypes.URL);
      expect(getAutoMatchedFieldType('link')).toBe(FieldTypes.URL);
      expect(getAutoMatchedFieldType('site')).toBe(FieldTypes.URL);
      expect(getAutoMatchedFieldType('WEBSITE_URL')).toBe(FieldTypes.URL);
    });

    it('should match number field types correctly', () => {
      expect(getAutoMatchedFieldType('number')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('count')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('quantity')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('amount')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('price')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('cost')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('total')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('sum')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('value')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('ITEM_COUNT')).toBe(FieldTypes.NUMBER);
    });

    it('should match percent field types correctly', () => {
      expect(getAutoMatchedFieldType('percent')).toBe(FieldTypes.PERCENT);
      expect(getAutoMatchedFieldType('percentage')).toBe(FieldTypes.PERCENT);
      expect(getAutoMatchedFieldType('rate')).toBe(FieldTypes.PERCENT);
      expect(getAutoMatchedFieldType('ratio')).toBe(FieldTypes.PERCENT);
      expect(getAutoMatchedFieldType('completion%')).toBe(FieldTypes.PERCENT);
      expect(getAutoMatchedFieldType('SUCCESS_RATE')).toBe(FieldTypes.PERCENT);
    });

    it('should match currency field types correctly', () => {
      expect(getAutoMatchedFieldType('currency')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('money')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('dollar')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('yen')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('euro')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('salary')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('wage')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('fee')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('payment')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('revenue')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('income')).toBe(FieldTypes.CURRENCY);
      expect(getAutoMatchedFieldType('MONTHLY_SALARY')).toBe(FieldTypes.CURRENCY);
    });

    it('should match datetime field types correctly', () => {
      expect(getAutoMatchedFieldType('date')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('time')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('birthday')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('birth')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('anniversary')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('deadline')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('due')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('start')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('end')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('schedule')).toBe(FieldTypes.DATETIME);
      expect(getAutoMatchedFieldType('CREATED_DATE')).toBe(FieldTypes.DATETIME);
    });

    it('should match line field types correctly', () => {
      expect(getAutoMatchedFieldType('line')).toBe(FieldTypes.LINE);
      expect(getAutoMatchedFieldType('lineid')).toBe(FieldTypes.LINE);
      expect(getAutoMatchedFieldType('chat')).toBe(FieldTypes.LINE);
      expect(getAutoMatchedFieldType('messaging')).toBe(FieldTypes.LINE);
      expect(getAutoMatchedFieldType('LINE_ID')).toBe(FieldTypes.LINE);
    });

    it('should match single select field types correctly', () => {
      expect(getAutoMatchedFieldType('status')).toBe(FieldTypes.SINGLE_SELECT);
      expect(getAutoMatchedFieldType('category')).toBe(FieldTypes.SINGLE_SELECT);
      expect(getAutoMatchedFieldType('type')).toBe(FieldTypes.SINGLE_SELECT);
      expect(getAutoMatchedFieldType('selection')).toBe(FieldTypes.SINGLE_SELECT);
      expect(getAutoMatchedFieldType('option')).toBe(FieldTypes.SINGLE_SELECT);
      expect(getAutoMatchedFieldType('choice')).toBe(FieldTypes.SINGLE_SELECT);
      expect(getAutoMatchedFieldType('pick')).toBe(FieldTypes.SINGLE_SELECT);
      expect(getAutoMatchedFieldType('dropdown')).toBe(FieldTypes.SINGLE_SELECT);
      expect(getAutoMatchedFieldType('select')).toBe(FieldTypes.SINGLE_SELECT);
      expect(getAutoMatchedFieldType('USER_STATUS')).toBe(FieldTypes.SINGLE_SELECT);
    });

    it('should match multi select field types correctly', () => {
      expect(getAutoMatchedFieldType('tags')).toBe(FieldTypes.MULTI_SELECT);
      expect(getAutoMatchedFieldType('categories')).toBe(FieldTypes.MULTI_SELECT);
      expect(getAutoMatchedFieldType('selections')).toBe(FieldTypes.MULTI_SELECT);
      expect(getAutoMatchedFieldType('options')).toBe(FieldTypes.MULTI_SELECT);
      expect(getAutoMatchedFieldType('choices')).toBe(FieldTypes.MULTI_SELECT);
      expect(getAutoMatchedFieldType('multiple')).toBe(FieldTypes.MULTI_SELECT);
      expect(getAutoMatchedFieldType('multi')).toBe(FieldTypes.MULTI_SELECT);
      expect(getAutoMatchedFieldType('list')).toBe(FieldTypes.MULTI_SELECT);
      expect(getAutoMatchedFieldType('USER_TAGS')).toBe(FieldTypes.MULTI_SELECT);
    });

    it('should match single line text field types correctly', () => {
      expect(getAutoMatchedFieldType('name')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('title')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('label')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('address')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('street')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('city')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('company')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('USER_NAME')).toBe(FieldTypes.SINGLE_LINE_TEXT);
    });

    it('should match long text field types correctly', () => {
      expect(getAutoMatchedFieldType('description')).toBe(FieldTypes.LONG_TEXT);
      expect(getAutoMatchedFieldType('note')).toBe(FieldTypes.LONG_TEXT);
      expect(getAutoMatchedFieldType('comment')).toBe(FieldTypes.LONG_TEXT);
      expect(getAutoMatchedFieldType('remarks')).toBe(FieldTypes.LONG_TEXT);
      expect(getAutoMatchedFieldType('details')).toBe(FieldTypes.LONG_TEXT);
      expect(getAutoMatchedFieldType('bio')).toBe(FieldTypes.LONG_TEXT);
      // 'summary' contains 'sum' which matches the number pattern first
      expect(getAutoMatchedFieldType('summary')).toBe(FieldTypes.NUMBER);
      expect(getAutoMatchedFieldType('content')).toBe(FieldTypes.LONG_TEXT);
      expect(getAutoMatchedFieldType('USER_DESCRIPTION')).toBe(FieldTypes.LONG_TEXT);
    });

    it('should match checkbox field types correctly', () => {
      expect(getAutoMatchedFieldType('active')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('enabled')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('published')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('verified')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('confirmed')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('approved')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('completed')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('isActive')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('hasPermission')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('canEdit')).toBe(FieldTypes.CHECKBOX);
      expect(getAutoMatchedFieldType('shouldNotify')).toBe(FieldTypes.CHECKBOX);
    });

    it('should default to single line text for unknown field types', () => {
      expect(getAutoMatchedFieldType('unknown_field')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('random')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('')).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(getAutoMatchedFieldType('xyz123')).toBe(FieldTypes.SINGLE_LINE_TEXT);
    });

    it('should handle case insensitive matching', () => {
      expect(getAutoMatchedFieldType('EMAIL')).toBe(FieldTypes.EMAIL);
      expect(getAutoMatchedFieldType('Phone')).toBe(FieldTypes.PHONE_NUMBER);
      expect(getAutoMatchedFieldType('URL')).toBe(FieldTypes.URL);
      expect(getAutoMatchedFieldType('Date')).toBe(FieldTypes.DATETIME);
    });

    it('should handle field names with extra whitespace', () => {
      expect(getAutoMatchedFieldType('  email  ')).toBe(FieldTypes.EMAIL);
      expect(getAutoMatchedFieldType(' phone number ')).toBe(FieldTypes.PHONE_NUMBER);
      expect(getAutoMatchedFieldType('\tdate\t')).toBe(FieldTypes.DATETIME);
    });
  });

  describe('getCsvFields', () => {
    it('should extract field names from CSV data', () => {
      const csvData = [
        ['Name', 'Email', 'Phone'],
        ['John Doe', '<EMAIL>', '************'],
        ['Jane Smith', '<EMAIL>', '************'],
      ];
      const result = getCsvFields(csvData);
      expect(result).toEqual(['Name', 'Email', 'Phone']);
    });

    it('should return empty array for empty CSV data', () => {
      expect(getCsvFields([])).toEqual([]);
    });

    it('should return empty array for null/undefined CSV data', () => {
      expect(getCsvFields(null as any)).toEqual([]);
      expect(getCsvFields(undefined as any)).toEqual([]);
    });

    it('should handle CSV data with non-array first row', () => {
      const csvData = [null as any, ['John', '<EMAIL>']];
      const result = getCsvFields(csvData);
      expect(result).toEqual([]);
    });

    it('should handle single column CSV', () => {
      const csvData = [['Name'], ['John'], ['Jane']];
      const result = getCsvFields(csvData);
      expect(result).toEqual(['Name']);
    });
  });

  describe('matchCsvFieldsToObjectFields', () => {
    const mockObjectFields = [
      { id: 'field1', name: 'Full Name', type: FieldTypes.SINGLE_LINE_TEXT },
      { id: 'field2', name: 'Email Address', type: FieldTypes.EMAIL },
      { id: 'field3', name: 'Phone Number', type: FieldTypes.PHONE_NUMBER },
    ];

    it('should match CSV fields to existing object fields by name', () => {
      const csvFields = ['Full Name', 'Email Address', 'Age'];
      const result = matchCsvFieldsToObjectFields(csvFields, mockObjectFields);

      expect(result).toHaveLength(3);
      expect(result[0]).toEqual({
        csvField: 'Full Name',
        systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
        systemFieldId: 'field1',
        systemFieldName: 'Full Name',
        skipped: false,
        isExistingField: true,
      });
      expect(result[1]).toEqual({
        csvField: 'Email Address',
        systemFieldType: FieldTypes.EMAIL,
        systemFieldId: 'field2',
        systemFieldName: 'Email Address',
        skipped: false,
        isExistingField: true,
      });
      // 'Age' doesn't match number patterns, so it defaults to SINGLE_LINE_TEXT
      expect(result[2]).toEqual({
        csvField: 'Age',
        systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
        systemFieldId: null,
        systemFieldName: null,
        skipped: false,
        isExistingField: false,
      });
    });

    it('should handle partial name matching', () => {
      const csvFields = ['Name', 'Email', 'Phone'];
      const result = matchCsvFieldsToObjectFields(csvFields, mockObjectFields);

      expect(result[0].isExistingField).toBe(true);
      expect(result[0].systemFieldId).toBe('field1');
      expect(result[1].isExistingField).toBe(true);
      expect(result[1].systemFieldId).toBe('field2');
      expect(result[2].isExistingField).toBe(true);
      expect(result[2].systemFieldId).toBe('field3');
    });

    it('should handle case-insensitive matching', () => {
      const csvFields = ['full name', 'EMAIL ADDRESS'];
      const result = matchCsvFieldsToObjectFields(csvFields, mockObjectFields);

      expect(result[0].isExistingField).toBe(true);
      expect(result[0].systemFieldId).toBe('field1');
      expect(result[1].isExistingField).toBe(true);
      expect(result[1].systemFieldId).toBe('field2');
    });

    it('should auto-match when no existing field matches', () => {
      const csvFields = ['Description', 'Website'];
      const result = matchCsvFieldsToObjectFields(csvFields, mockObjectFields);

      expect(result[0]).toEqual({
        csvField: 'Description',
        systemFieldType: FieldTypes.LONG_TEXT,
        systemFieldId: null,
        systemFieldName: null,
        skipped: false,
        isExistingField: false,
      });
      expect(result[1]).toEqual({
        csvField: 'Website',
        systemFieldType: FieldTypes.URL,
        systemFieldId: null,
        systemFieldName: null,
        skipped: false,
        isExistingField: false,
      });
    });

    it('should handle empty CSV fields', () => {
      const result = matchCsvFieldsToObjectFields([], mockObjectFields);
      expect(result).toEqual([]);
    });

    it('should handle empty object fields', () => {
      const csvFields = ['Name', 'Email'];
      const result = matchCsvFieldsToObjectFields(csvFields, []);

      expect(result).toHaveLength(2);
      expect(result[0].isExistingField).toBe(false);
      expect(result[1].isExistingField).toBe(false);
    });

    it('should handle null/undefined parameters', () => {
      const result1 = matchCsvFieldsToObjectFields(null as any, mockObjectFields);
      expect(result1).toEqual([]);

      const result2 = matchCsvFieldsToObjectFields(['Name'], null as any);
      expect(result2).toHaveLength(1);
      expect(result2[0].isExistingField).toBe(false);
    });
  });

  describe('createInitialFieldMappings', () => {
    it('should create initial field mappings', () => {
      const csvFields = ['Name', 'Email'];
      const objectFields = [{ id: 'field1', name: 'Name', type: FieldTypes.SINGLE_LINE_TEXT }];
      const result = createInitialFieldMappings(csvFields, objectFields);

      expect(result).toHaveLength(2);
      expect(result[0].isExistingField).toBe(true);
      expect(result[1].isExistingField).toBe(false);
    });
  });

  describe('calculateTotalRecords', () => {
    it('should calculate total records correctly', () => {
      const csvData = [
        ['Name', 'Email'],
        ['John', '<EMAIL>'],
        ['Jane', '<EMAIL>'],
      ];
      expect(calculateTotalRecords(csvData)).toBe(3);
    });

    it('should return 0 for empty CSV data', () => {
      expect(calculateTotalRecords([])).toBe(0);
    });

    it('should handle single row CSV', () => {
      const csvData = [['Name', 'Email']];
      expect(calculateTotalRecords(csvData)).toBe(1);
    });
  });

  describe('filterAutoCreatedFields', () => {
    it('should filter out auto-created fields', () => {
      const fieldOptions = [
        { value: FieldTypes.SINGLE_LINE_TEXT, label: 'Single Line Text' },
        { value: FieldTypes.EMAIL, label: 'Email' },
        { value: FieldTypes.CREATED_TIME, label: 'Created Time' },
        { value: FieldTypes.MODIFIED_TIME, label: 'Modified Time' },
        { value: FieldTypes.CREATED_BY, label: 'Created By' },
        { value: FieldTypes.MODIFIED_BY, label: 'Modified By' },
        { value: FieldTypes.AUTONUMBER, label: 'Auto Number' },
        { value: FieldTypes.PHONE_NUMBER, label: 'Phone Number' },
      ];

      const result = filterAutoCreatedFields(fieldOptions);
      expect(result).toHaveLength(3);
      expect(result.map((option) => option.value)).toEqual([
        FieldTypes.SINGLE_LINE_TEXT,
        FieldTypes.EMAIL,
        FieldTypes.PHONE_NUMBER,
      ]);
    });

    it('should return empty array for empty input', () => {
      expect(filterAutoCreatedFields([])).toEqual([]);
    });
  });

  describe('updateFieldMapping', () => {
    let mockFieldMappings: FieldMapping[];

    beforeEach(() => {
      mockFieldMappings = [
        {
          csvField: 'Name',
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
          systemFieldId: null,
          systemFieldName: null,
          skipped: false,
          isExistingField: false,
        },
        {
          csvField: 'Email',
          systemFieldType: FieldTypes.EMAIL,
          systemFieldId: null,
          systemFieldName: null,
          skipped: false,
          isExistingField: false,
        },
      ];
    });

    it('should update field mapping to existing field', () => {
      const result = updateFieldMapping(mockFieldMappings, 'Name', {
        systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
        systemFieldId: 'field1',
        systemFieldName: 'Full Name',
      });

      expect(result[0]).toEqual({
        csvField: 'Name',
        systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
        systemFieldId: 'field1',
        systemFieldName: 'Full Name',
        skipped: false,
        isExistingField: true,
      });
    });

    it('should update field mapping to new field type', () => {
      const result = updateFieldMapping(mockFieldMappings, 'Name', {
        systemFieldType: FieldTypes.LONG_TEXT,
      });

      expect(result[0]).toEqual({
        csvField: 'Name',
        systemFieldType: FieldTypes.LONG_TEXT,
        systemFieldId: null,
        systemFieldName: null,
        skipped: false,
        isExistingField: false,
      });
    });

    it('should update skipped status', () => {
      const result = updateFieldMapping(mockFieldMappings, 'Email', {
        skipped: true,
      });

      expect(result[1].skipped).toBe(true);
    });

    it('should not modify other field mappings', () => {
      const result = updateFieldMapping(mockFieldMappings, 'Name', {
        skipped: true,
      });

      expect(result[1]).toEqual(mockFieldMappings[1]);
    });

    it('should handle non-existent CSV field', () => {
      const result = updateFieldMapping(mockFieldMappings, 'NonExistent', {
        skipped: true,
      });

      expect(result).toEqual(mockFieldMappings);
    });
  });

  describe('getFieldMappingByField', () => {
    const mockFieldMappings: FieldMapping[] = [
      {
        csvField: 'Name',
        systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
        systemFieldId: null,
        systemFieldName: null,
        skipped: false,
        isExistingField: false,
      },
      {
        csvField: 'Email',
        systemFieldType: FieldTypes.EMAIL,
        systemFieldId: null,
        systemFieldName: null,
        skipped: false,
        isExistingField: false,
      },
    ];

    it('should return correct field mapping', () => {
      const result = getFieldMappingByField(mockFieldMappings, 'Name');
      expect(result).toEqual(mockFieldMappings[0]);
    });

    it('should return undefined for non-existent field', () => {
      const result = getFieldMappingByField(mockFieldMappings, 'NonExistent');
      expect(result).toBeUndefined();
    });
  });

  describe('getActiveFieldMappings', () => {
    const mockFieldMappings: FieldMapping[] = [
      {
        csvField: 'Name',
        systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
        systemFieldId: null,
        systemFieldName: null,
        skipped: false,
        isExistingField: false,
      },
      {
        csvField: 'Email',
        systemFieldType: FieldTypes.EMAIL,
        systemFieldId: null,
        systemFieldName: null,
        skipped: true,
        isExistingField: false,
      },
      {
        csvField: 'Phone',
        systemFieldType: FieldTypes.PHONE_NUMBER,
        systemFieldId: null,
        systemFieldName: null,
        skipped: false,
        isExistingField: false,
      },
    ];

    it('should return only non-skipped field mappings', () => {
      const result = getActiveFieldMappings(mockFieldMappings);
      expect(result).toHaveLength(2);
      expect(result[0].csvField).toBe('Name');
      expect(result[1].csvField).toBe('Phone');
    });

    it('should return empty array when all fields are skipped', () => {
      const allSkipped = mockFieldMappings.map((mapping) => ({ ...mapping, skipped: true }));
      const result = getActiveFieldMappings(allSkipped);
      expect(result).toEqual([]);
    });
  });

  describe('getSkippedFieldMappings', () => {
    const mockFieldMappings: FieldMapping[] = [
      {
        csvField: 'Name',
        systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
        systemFieldId: null,
        systemFieldName: null,
        skipped: false,
        isExistingField: false,
      },
      {
        csvField: 'Email',
        systemFieldType: FieldTypes.EMAIL,
        systemFieldId: null,
        systemFieldName: null,
        skipped: true,
        isExistingField: false,
      },
    ];

    it('should return only skipped field mappings', () => {
      const result = getSkippedFieldMappings(mockFieldMappings);
      expect(result).toHaveLength(1);
      expect(result[0].csvField).toBe('Email');
    });

    it('should return empty array when no fields are skipped', () => {
      const noneSkipped = mockFieldMappings.map((mapping) => ({ ...mapping, skipped: false }));
      const result = getSkippedFieldMappings(noneSkipped);
      expect(result).toEqual([]);
    });
  });

  describe('validateFieldMappings', () => {
    it('should validate successful field mappings', () => {
      const validMappings: FieldMapping[] = [
        {
          csvField: 'Name',
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
          systemFieldId: null,
          systemFieldName: null,
          skipped: false,
          isExistingField: false,
        },
      ];

      const result = validateFieldMappings(validMappings);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should fail validation when no fields are mapped', () => {
      const allSkipped: FieldMapping[] = [
        {
          csvField: 'Name',
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
          systemFieldId: null,
          systemFieldName: null,
          skipped: true,
          isExistingField: false,
        },
      ];

      const result = validateFieldMappings(allSkipped);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('At least one field must be mapped for import');
    });

    it('should fail validation for empty mappings', () => {
      const result = validateFieldMappings([]);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('At least one field must be mapped for import');
    });

    it('should validate mixed skipped and non-skipped fields', () => {
      const mixedMappings: FieldMapping[] = [
        {
          csvField: 'Name',
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
          systemFieldId: null,
          systemFieldName: null,
          skipped: false,
          isExistingField: false,
        },
        {
          csvField: 'Email',
          systemFieldType: FieldTypes.EMAIL,
          systemFieldId: null,
          systemFieldName: null,
          skipped: true,
          isExistingField: false,
        },
      ];

      const result = validateFieldMappings(mixedMappings);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });
  });

  describe('createFieldPayload', () => {
    it('should create field payload for new fields only', () => {
      const fieldMappings: FieldMapping[] = [
        {
          csvField: 'Name',
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
          systemFieldId: 'existing1',
          systemFieldName: 'Full Name',
          skipped: false,
          isExistingField: true,
        },
        {
          csvField: 'Email',
          systemFieldType: FieldTypes.EMAIL,
          systemFieldId: null,
          systemFieldName: null,
          skipped: false,
          isExistingField: false,
        },
        {
          csvField: 'Age',
          systemFieldType: FieldTypes.NUMBER,
          systemFieldId: null,
          systemFieldName: 'User Age',
          skipped: false,
          isExistingField: false,
        },
      ];

      const result = createFieldPayload(fieldMappings);
      expect(result).toHaveLength(2);
      expect(result).toEqual([
        { name: 'Email', type: FieldTypes.EMAIL },
        { name: 'User Age', type: FieldTypes.NUMBER },
      ]);
    });

    it('should exclude skipped fields from payload', () => {
      const fieldMappings: FieldMapping[] = [
        {
          csvField: 'Email',
          systemFieldType: FieldTypes.EMAIL,
          systemFieldId: null,
          systemFieldName: null,
          skipped: false,
          isExistingField: false,
        },
        {
          csvField: 'Phone',
          systemFieldType: FieldTypes.PHONE_NUMBER,
          systemFieldId: null,
          systemFieldName: null,
          skipped: true,
          isExistingField: false,
        },
      ];

      const result = createFieldPayload(fieldMappings);
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({ name: 'Email', type: FieldTypes.EMAIL });
    });

    it('should handle empty field mappings', () => {
      const result = createFieldPayload([]);
      expect(result).toEqual([]);
    });

    it('should handle all skipped fields', () => {
      const fieldMappings: FieldMapping[] = [
        {
          csvField: 'Email',
          systemFieldType: FieldTypes.EMAIL,
          systemFieldId: null,
          systemFieldName: null,
          skipped: true,
          isExistingField: false,
        },
      ];

      const result = createFieldPayload(fieldMappings);
      expect(result).toEqual([]);
    });
  });

  describe('createFieldsToConfirmMap', () => {
    it('should map both system and new fields correctly', () => {
      const activeFieldMappings: FieldMapping[] = [
        {
          csvField: 'Name',
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
          systemFieldId: 'sys1',
          systemFieldName: 'Full Name',
          skipped: false,
          isExistingField: true,
        },
        {
          csvField: 'Email',
          systemFieldType: FieldTypes.EMAIL,
          systemFieldId: null,
          systemFieldName: 'User Email',
          skipped: false,
          isExistingField: false,
        },
      ];

      const responseFields = [{ id: 'new1', name: 'User Email' }];

      const result = createFieldsToConfirmMap(activeFieldMappings, responseFields);
      expect(result).toEqual({
        sys1: 'Name',
        new1: 'Email',
      });
    });

    it('should handle only system fields', () => {
      const activeFieldMappings: FieldMapping[] = [
        {
          csvField: 'Name',
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
          systemFieldId: 'sys1',
          systemFieldName: 'Full Name',
          skipped: false,
          isExistingField: true,
        },
      ];

      const result = createFieldsToConfirmMap(activeFieldMappings, []);
      expect(result).toEqual({
        sys1: 'Name',
      });
    });

    it('should handle only new fields', () => {
      const activeFieldMappings: FieldMapping[] = [
        {
          csvField: 'Email',
          systemFieldType: FieldTypes.EMAIL,
          systemFieldId: null,
          systemFieldName: 'User Email',
          skipped: false,
          isExistingField: false,
        },
      ];

      const responseFields = [{ id: 'new1', name: 'User Email' }];

      const result = createFieldsToConfirmMap(activeFieldMappings, responseFields);
      expect(result).toEqual({
        new1: 'Email',
      });
    });

    it('should handle empty inputs', () => {
      expect(createFieldsToConfirmMap([], [])).toEqual({});
    });

    it('should handle unmatched new fields', () => {
      const activeFieldMappings: FieldMapping[] = [
        {
          csvField: 'Email',
          systemFieldType: FieldTypes.EMAIL,
          systemFieldId: null,
          systemFieldName: 'User Email',
          skipped: false,
          isExistingField: false,
        },
      ];

      const responseFields = [{ id: 'new1', name: 'Different Name' }];

      const result = createFieldsToConfirmMap(activeFieldMappings, responseFields);
      expect(result).toEqual({});
    });
  });

  describe('normalizeFieldMappings', () => {
    it('should normalize field mappings with correct options', () => {
      const fields = [
        { name: '  Name  ', type: FieldTypes.SINGLE_LINE_TEXT },
        { name: 'Date', type: FieldTypes.DATETIME },
        { name: 'Price', type: FieldTypes.CURRENCY },
      ];

      const result = normalizeFieldMappings(fields);
      expect(result).toHaveLength(3);
      expect(result[0].name).toBe('Name');
      expect(result[0].type).toBe(FieldTypes.SINGLE_LINE_TEXT);
      expect(result[0].options).toEqual({});

      expect(result[1].options).toEqual({
        date: { format: 'YYYY/MM/DD' },
        time: { format: 'hh:mm A' },
        timezone: { format: 'Asia/Tokyo' },
      });

      expect(result[2].options).toEqual({
        currency: 'USD',
        decimalPlaces: 1,
        separator: { format: 'commaPeriod' },
      });
    });

    it('should handle select field types', () => {
      const fields = [
        { name: 'Status', type: FieldTypes.SINGLE_SELECT },
        { name: 'Tags', type: FieldTypes.MULTI_SELECT },
      ];

      const result = normalizeFieldMappings(fields);
      expect(result[0].options).toEqual({ choices: [] });
      expect(result[1].options).toEqual({ choices: [] });
    });

    it('should handle number field types', () => {
      const fields = [
        { name: 'Count', type: FieldTypes.NUMBER },
        { name: 'Percentage', type: FieldTypes.PERCENT },
      ];

      const result = normalizeFieldMappings(fields);
      expect(result[0].options).toEqual({ numberFormat: 'decimal' });
      expect(result[1].options).toEqual({ separator: { format: 'commaPeriod' } });
    });

    it('should handle phone number field types', () => {
      const fields = [{ name: 'Phone', type: FieldTypes.PHONE_NUMBER }];

      const result = normalizeFieldMappings(fields);
      expect(result[0].options).toEqual({
        customFormat: { enabled: true, format: 'jp' },
      });
    });

    it('should handle unknown field types with default options', () => {
      const fields = [{ name: 'Unknown', type: 'unknown_type' as any }];

      const result = normalizeFieldMappings(fields);
      expect(result[0].options).toEqual({});
    });
  });
});
