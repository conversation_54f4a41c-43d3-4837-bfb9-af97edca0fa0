import { useToolbar } from '@/contexts/ToolbarContext';
import useChatWindow from '@/hooks/useChatWindow';
import { Modal, Stack, Text, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { type Dispatch, type SetStateAction, useEffect, useRef } from 'react';
import ExistingChatwindowContent from './ExistingChatwindowContent';
import NoChatwindowContent from './NoChatwindowContent';

interface ConnectChatwindowModalProps {
  opened: boolean;
  onClose: () => void;
  setModalRef?: Dispatch<SetStateAction<HTMLDivElement | null>>;
  integrationData?: any; // For update mode
  onSuccess?: () => void; // Callback for successful operations
}

const ConnectChatwindowModal = ({
  opened,
  onClose,
  integrationData,
  onSuccess,
}: ConnectChatwindowModalProps) => {
  const { t } = useTranslate('integrations');
  const modalRef = useRef<HTMLDivElement | null>(null);
  const { chatwindow } = useChatWindow();
  const hasChatwindow = !!chatwindow?.length;
  const { addOutsideRef } = useToolbar();

  useEffect(() => {
    addOutsideRef(modalRef.current);
  }, [opened]);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      centered
      size={rem(500)}
      closeOnClickOutside={false}
      title={
        <Text size='md' fw={500}>
          {t('chattingwindowIntegration')}
        </Text>
      }
      ref={modalRef}
    >
      <Stack gap='md'>
        {hasChatwindow ? (
          <ExistingChatwindowContent
            chatwindowData={chatwindow}
            integrationData={integrationData}
            onClose={onClose}
            onSuccess={onSuccess}
          />
        ) : (
          <NoChatwindowContent onClose={onClose} />
        )}
      </Stack>
    </Modal>
  );
};

export default ConnectChatwindowModal;
