import ChattingWindowImage from '@/assets/images/chatting-window-connect.svg';
import ImageMessage from '@/assets/images/img-messges.svg';
import { Flex, Image, Text, rem } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui/components/DecaButton';
import { useTranslate } from '@tolgee/react';

const NoChatwindowContent = ({ onClose }: { onClose: () => void }) => {
  const { t } = useTranslate('integrations');

  return (
    <>
      <Image src={ChattingWindowImage} width={238} />

      <Image src={ImageMessage} style={{ width: 60, height: 60 }} />

      <Text size='md' c='dimmed'>
        {t('noChatwindowFound')}
      </Text>

      <Text size='md' c='dimmed'>
        {t('noChatwindowDescription')}
      </Text>
      <Flex gap={rem(12)} justify='flex-end' mt={rem(20)} w='100%'>
        <DecaButton variant='neutral' onClick={onClose}>
          {t('cancel')}
        </DecaButton>
        <DecaButton variant='primary' disabled={true}>
          {t('connect')}
        </DecaButton>
      </Flex>
    </>
  );
};

export default NoChatwindowContent;
