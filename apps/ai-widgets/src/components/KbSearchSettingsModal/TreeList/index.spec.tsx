import { screen } from '@testing-library/react';
import type { NodeApi } from 'react-arborist';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import type { TreeItem, Trees } from '@/types';
import { renderWithMantine } from '@/utils/test';
import TreeList from './index';

// Mock react-arborist Tree component
vi.mock('react-arborist', () => ({
  Tree: vi.fn(({ children, data, className, ...props }) => {
    // Simulate tree rendering with mock nodes
    const mockNodes = data || [];
    return (
      <div data-testid='tree-container' className={className} {...props}>
        {mockNodes.map((item: TreeItem, index: number) => {
          const mockNode: NodeApi<TreeItem> = {
            data: item,
            level: 0,
            parent: null,
            children: [],
            isLeaf: !item.childFolderCount && !item.childKbCount,
            tree: {} as any,
            id: item.id,
            isDraggable: false,
            rowIndex: index,
            isRoot: false,
            isInternal: false,
            isOpen: false,
            isClosed: true,
            isEditable: false,
            isEditing: false,
            isSelected: false,
            isOnlySelection: false,
            isSelectedStart: false,
            isSelectedEnd: false,
            isFocused: false,
            isDragging: false,
            willReceiveDrop: false,
            state: {
              isClosed: true,
              isDragging: false,
              isEditing: false,
              isFocused: false,
              isInternal: false,
              isLeaf: !item.childFolderCount && !item.childKbCount,
              isOpen: false,
              isSelected: false,
              isSelectedEnd: false,
              isSelectedStart: false,
              willReceiveDrop: false,
            },
            childIndex: 0,
            next: null,
            prev: null,
            nextSibling: null,
            isAncestorOf: vi.fn(),
            select: vi.fn(),
            deselect: vi.fn(),
            selectMulti: vi.fn(),
            selectContiguous: vi.fn(),
            activate: vi.fn(),
            focus: vi.fn(),
            toggle: vi.fn(),
            open: vi.fn(),
            openParents: vi.fn(),
            close: vi.fn(),
            submit: vi.fn(),
            reset: vi.fn(),
            clone: vi.fn(),
            edit: vi.fn(),
            handleClick: vi.fn(),
          };

          const mockStyle = {
            position: 'absolute' as const,
            top: index * 32,
            height: 32,
            width: '100%',
          };

          return children({
            node: mockNode,
            style: mockStyle,
            dragHandle: vi.fn(),
          });
        })}
      </div>
    );
  }),
}));

// Mock TreeLeaf component
vi.mock('./TreeLeaf', () => ({
  default: vi.fn(({ node, rightItem }) => (
    <div data-testid={`tree-leaf-${node.data.id}`} data-node-name={node.data.name}>
      <span>{node.data.name}</span>
      {rightItem && <div data-testid='right-item'>{rightItem(node)}</div>}
    </div>
  )),
}));

describe('TreeList Component', () => {
  const mockTreeData: Trees = [
    {
      id: 'folder-1',
      path: '/folder1',
      name: 'Folder 1',
      count: 5,
      childFolderCount: 2,
      childKbCount: 1,
      parentDirId: 'root',
      children: [
        {
          id: 'subfolder-1',
          path: '/folder1/subfolder1',
          name: 'Subfolder 1',
          count: 2,
          childFolderCount: 0,
          childKbCount: 2,
          parentDirId: 'folder-1',
        },
      ],
    },
    {
      id: 'folder-2',
      path: '/folder2',
      name: 'Empty Folder',
      count: 0,
      childFolderCount: 0,
      childKbCount: 0,
      parentDirId: 'root',
    },
  ];

  const mockRightItem = vi.fn((node: NodeApi<TreeItem>) => (
    <button type='button' data-testid={`action-button-${node.data.id}`}>
      Action
    </button>
  ));

  const mockOnPrefetchChildFolders = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders without crashing', () => {
      renderWithMantine(<TreeList />);
      expect(screen.getByTestId('tree-container')).toBeInTheDocument();
    });

    it('renders with tree data', () => {
      renderWithMantine(<TreeList tree={mockTreeData} />);

      expect(screen.getByTestId('tree-container')).toBeInTheDocument();
      expect(screen.getByTestId('tree-leaf-folder-1')).toBeInTheDocument();
      expect(screen.getByTestId('tree-leaf-folder-2')).toBeInTheDocument();
    });

    it('renders tree leaf nodes with correct names', () => {
      renderWithMantine(<TreeList tree={mockTreeData} />);

      expect(screen.getByText('Folder 1')).toBeInTheDocument();
      expect(screen.getByText('Empty Folder')).toBeInTheDocument();
    });

    it('renders right items when provided', () => {
      renderWithMantine(<TreeList tree={mockTreeData} rightItem={mockRightItem} />);

      expect(screen.getByTestId('action-button-folder-1')).toBeInTheDocument();
      expect(screen.getByTestId('action-button-folder-2')).toBeInTheDocument();
    });
  });

  describe('Props Handling', () => {
    it('passes tree data to Tree component', () => {
      renderWithMantine(<TreeList tree={mockTreeData} />);

      // Verify that tree data is rendered
      expect(screen.getByTestId('tree-leaf-folder-1')).toBeInTheDocument();
      expect(screen.getByTestId('tree-leaf-folder-2')).toBeInTheDocument();
    });

    it('passes rightItem function to TreeLeaf components', () => {
      renderWithMantine(<TreeList tree={mockTreeData} rightItem={mockRightItem} />);

      expect(screen.getAllByTestId(/^right-item$/)).toHaveLength(2);
      expect(mockRightItem).toHaveBeenCalledTimes(2);
    });

    it('handles onPrefetchChildFolders prop', () => {
      renderWithMantine(
        <TreeList tree={mockTreeData} onPrefetchChildFolders={mockOnPrefetchChildFolders} />
      );

      // The onPrefetchChildFolders prop should be available in the component
      // even though it's not directly used in TreeList itself
      expect(screen.getByTestId('tree-container')).toBeInTheDocument();
    });
  });

  describe('Empty States', () => {
    it('renders without tree data', () => {
      renderWithMantine(<TreeList />);

      expect(screen.getByTestId('tree-container')).toBeInTheDocument();
    });

    it('renders with empty tree array', () => {
      renderWithMantine(<TreeList tree={[]} />);

      expect(screen.getByTestId('tree-container')).toBeInTheDocument();
    });
  });

  describe('TreeLeaf Integration', () => {
    it('passes correct props to TreeLeaf components', async () => {
      // Get the mocked TreeLeaf component
      const { default: TreeLeaf } = await import('./TreeLeaf');

      renderWithMantine(<TreeList tree={mockTreeData} rightItem={mockRightItem} />);

      // Verify TreeLeaf was called with correct props
      expect(TreeLeaf).toHaveBeenCalledWith(
        expect.objectContaining({
          node: expect.objectContaining({
            data: expect.objectContaining({
              id: 'folder-1',
              name: 'Folder 1',
            }),
          }),
          style: expect.objectContaining({
            position: 'absolute',
            height: 32,
            width: '100%',
          }),
          dragHandle: expect.any(Function),
          rightItem: mockRightItem,
        }),
        expect.any(Object)
      );
    });

    it('renders TreeLeaf for each tree item', async () => {
      const { default: TreeLeaf } = await import('./TreeLeaf');

      renderWithMantine(<TreeList tree={mockTreeData} />);

      expect(TreeLeaf).toHaveBeenCalledTimes(2);
    });
  });

  describe('Accessibility', () => {
    it('renders tree structure that can be navigated', () => {
      renderWithMantine(<TreeList tree={mockTreeData} />);

      const treeContainer = screen.getByTestId('tree-container');
      expect(treeContainer).toBeInTheDocument();

      // Verify tree items are accessible
      expect(screen.getByText('Folder 1')).toBeInTheDocument();
      expect(screen.getByText('Empty Folder')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('handles large tree data efficiently', () => {
      const largeTreeData: Trees = Array.from({ length: 100 }, (_, index) => ({
        id: `folder-${index}`,
        path: `/folder${index}`,
        name: `Folder ${index}`,
        count: index,
        childFolderCount: index % 2,
        childKbCount: index % 3,
        parentDirId: 'root',
      }));

      const { container } = renderWithMantine(<TreeList tree={largeTreeData} />);

      expect(container.querySelector('[data-testid="tree-container"]')).toBeInTheDocument();
      // Tree should render efficiently with virtualization
      expect(screen.getByTestId('tree-leaf-folder-0')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles tree data with nested children', () => {
      const nestedTreeData: Trees = [
        {
          id: 'parent',
          path: '/parent',
          name: 'Parent Folder',
          count: 10,
          childFolderCount: 3,
          childKbCount: 2,
          parentDirId: 'root',
          children: [
            {
              id: 'child-1',
              path: '/parent/child1',
              name: 'Child 1',
              count: 5,
              childFolderCount: 1,
              childKbCount: 1,
              parentDirId: 'parent',
              children: [
                {
                  id: 'grandchild',
                  path: '/parent/child1/grandchild',
                  name: 'Grandchild',
                  count: 2,
                  childFolderCount: 0,
                  childKbCount: 2,
                  parentDirId: 'child-1',
                },
              ],
            },
          ],
        },
      ];

      renderWithMantine(<TreeList tree={nestedTreeData} />);

      expect(screen.getByTestId('tree-leaf-parent')).toBeInTheDocument();
      expect(screen.getByText('Parent Folder')).toBeInTheDocument();
    });

    it('handles undefined rightItem gracefully', () => {
      renderWithMantine(<TreeList tree={mockTreeData} rightItem={undefined} />);

      expect(screen.getByTestId('tree-container')).toBeInTheDocument();
      expect(screen.queryByTestId(/^right-item$/)).not.toBeInTheDocument();
    });
  });
});
