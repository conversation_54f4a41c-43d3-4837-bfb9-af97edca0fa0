import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import type { CSSProperties } from 'react';
import type { Node<PERSON><PERSON> } from 'react-arborist';
import { beforeEach, describe, expect, it, vi, type MockedFunction } from 'vitest';

import type { TreeItem } from '@/types';
import { renderWithMantine } from '@/utils/test';
import { useKbWidgetContext } from '../../../../contexts/KbWidgetContext';

// Mock the context
vi.mock('../../../../contexts/KbWidgetContext');

// Mock the icons
vi.mock('@tabler/icons-react', () => ({
  IconChevronDown: ({ size, ...props }: any) => (
    <svg data-testid='icon-chevron-down' data-size={size} {...props}>
      <title>Chevron Down</title>
    </svg>
  ),
  IconChevronRight: ({ size, ...props }: any) => (
    <svg data-testid='icon-chevron-right' data-size={size} {...props}>
      <title>Chevron Right</title>
    </svg>
  ),
  IconFolder: ({ size, ...props }: any) => (
    <svg data-testid='icon-folder' data-size={size} {...props}>
      <title>Folder</title>
    </svg>
  ),
}));

// Mock the UI components
vi.mock('@resola-ai/ui/components', () => ({
  TooltipWithOverflowText: ({ text, ...props }: any) => (
    <span data-testid='tooltip-with-overflow-text' {...props}>
      {text}
    </span>
  ),
}));

// Mock the IconFolderEmpty component
vi.mock('../../Icons/IconFolderEmpty', () => ({
  IconFolderEmpty: (props: any) => (
    <svg data-testid='icon-folder-empty' {...props}>
      <title>Folder Empty</title>
    </svg>
  ),
}));

// Import the component after mocks
const TreeLeaf = (await import('./index')).default;

describe('TreeLeaf', () => {
  const mockContextValue = {
    fetchFolders: vi.fn(),
    fetchBases: vi.fn(),
    fetchDocuments: vi.fn(),
    setSelectedKb: vi.fn(),
    selectedFolder: '',
    setSelectedFolder: vi.fn(),
    setBases: vi.fn(),
  };

  const createMockNode = (overrides: Partial<NodeApi<TreeItem>> = {}): NodeApi<TreeItem> =>
    ({
      id: 'node-1',
      level: 0,
      isOpen: false,
      isSelected: false,
      isEditing: false,
      isLeaf: false,
      isDragging: false,
      isDraggable: true,
      willReceiveDrop: false,
      tree: {} as any,
      parent: null,
      children: [],
      data: {
        id: 'folder-1',
        name: 'Test Folder',
        path: '/test',
        count: 0,
        childFolderCount: 2,
        childKbCount: 1,
        parentDirId: 'parent-1',
      },
      open: vi.fn(),
      close: vi.fn(),
      toggle: vi.fn(),
      select: vi.fn(),
      deselect: vi.fn(),
      focus: vi.fn(),
      activate: vi.fn(),
      edit: vi.fn(),
      submit: vi.fn(),
      reset: vi.fn(),
      rowIndex: 0,
      isRoot: false,
      isInternal: false,
      isClosed: false,
      isEditable: false,
      isOnlySelection: false,
      isSelectedStart: false,
      isSelectedEnd: false,
      ...overrides,
    }) as NodeApi<TreeItem>;

  const defaultProps = {
    node: createMockNode(),
    style: { height: '32px' } as CSSProperties,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useKbWidgetContext as MockedFunction<typeof useKbWidgetContext>).mockReturnValue(
      mockContextValue as any
    );
  });

  describe('Basic Rendering', () => {
    it('should render with basic props', () => {
      renderWithMantine(<TreeLeaf {...defaultProps} />);

      expect(screen.getByTestId('tooltip-with-overflow-text')).toBeInTheDocument();
      expect(screen.getByText('Test Folder')).toBeInTheDocument();
    });
  });

  describe('Tree Arrow Logic', () => {
    it('should not render arrow for node without child folders', () => {
      const node = createMockNode({
        data: { ...defaultProps.node.data, childFolderCount: 0 },
      });
      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      expect(screen.queryByTestId('icon-chevron-down')).not.toBeInTheDocument();
      expect(screen.queryByTestId('icon-chevron-right')).not.toBeInTheDocument();
    });
  });

  describe('Folder Icons', () => {
    it('should render empty folder icon when folder has no children', () => {
      const node = createMockNode({
        data: { ...defaultProps.node.data, childKbCount: 0, childFolderCount: 0 },
      });
      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      expect(screen.getByTestId('icon-folder-empty')).toBeInTheDocument();
      expect(screen.queryByTestId('icon-folder')).not.toBeInTheDocument();
    });
  });

  describe('Styling and Selection', () => {
    it('should render correctly when node is selected', () => {
      const node = createMockNode({ isSelected: true });
      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const leafContainer = screen.getByTestId('tooltip-with-overflow-text').closest('div');
      expect(leafContainer).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-with-overflow-text')).toBeInTheDocument();
    });

    it('should render correctly when node is not selected', () => {
      const node = createMockNode({ isSelected: false });
      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const leafContainer = screen.getByTestId('tooltip-with-overflow-text').closest('div');
      expect(leafContainer).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-with-overflow-text')).toBeInTheDocument();
    });
  });

  describe('Hover Behavior', () => {
    it('should call fetchFolders on hover when conditions are met', async () => {
      const user = userEvent.setup();
      const node = createMockNode({
        level: 2,
        data: {
          ...defaultProps.node.data,
          childFolderCount: 3,
          children: undefined,
        },
      });

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const leafContainer = screen.getByTestId('tooltip-with-overflow-text').closest('div');
      if (leafContainer) {
        await user.hover(leafContainer);
      }

      expect(mockContextValue.fetchFolders).toHaveBeenCalledWith('folder-1', 1);
    });

    it('should not call fetchFolders on hover when node has no child folders', async () => {
      const user = userEvent.setup();
      const node = createMockNode({
        data: { ...defaultProps.node.data, childFolderCount: 0 },
      });

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const leafContainer = screen.getByTestId('tooltip-with-overflow-text').closest('div');
      if (leafContainer) {
        await user.hover(leafContainer);
      }

      expect(mockContextValue.fetchFolders).not.toHaveBeenCalled();
    });

    it('should not call fetchFolders on hover when level exceeds MAX_DEPTH', async () => {
      const user = userEvent.setup();
      const node = createMockNode({
        level: 6, // MAX_DEPTH is 5
        data: {
          ...defaultProps.node.data,
          childFolderCount: 3,
          children: undefined,
        },
      });

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const leafContainer = screen.getByTestId('tooltip-with-overflow-text').closest('div');
      if (leafContainer) {
        await user.hover(leafContainer);
      }

      expect(mockContextValue.fetchFolders).not.toHaveBeenCalled();
    });

    it('should not call fetchFolders on hover when node already has children', async () => {
      const user = userEvent.setup();
      const node = createMockNode({
        level: 2,
        data: {
          ...defaultProps.node.data,
          childFolderCount: 3,
          children: [{ id: 'child-1' } as TreeItem],
        },
      });

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const leafContainer = screen.getByTestId('tooltip-with-overflow-text').closest('div');
      if (leafContainer) {
        await user.hover(leafContainer);
      }

      expect(mockContextValue.fetchFolders).not.toHaveBeenCalled();
    });

    it('should only call fetchFolders once per component instance', async () => {
      const user = userEvent.setup();
      const node = createMockNode({
        level: 2,
        data: {
          ...defaultProps.node.data,
          childFolderCount: 3,
          children: undefined,
        },
      });

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const leafContainer = screen.getByTestId('tooltip-with-overflow-text').closest('div');
      if (leafContainer) {
        await user.hover(leafContainer);
        await user.hover(leafContainer);
        await user.hover(leafContainer);
      }

      expect(mockContextValue.fetchFolders).toHaveBeenCalledTimes(1);
    });
  });

  describe('Click Behavior', () => {
    it('should handle click correctly when folder is not currently selected', async () => {
      const user = userEvent.setup();
      const mockToggle = vi.fn();
      const node = createMockNode({
        toggle: mockToggle,
        data: { ...defaultProps.node.data, id: 'new-folder' },
      });

      // Set current selectedFolder to different value
      mockContextValue.selectedFolder = 'different-folder';

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const clickableArea = screen
        .getByTestId('tooltip-with-overflow-text')
        .closest('div')
        ?.querySelector('div[role="button"], div') as HTMLElement;
      if (clickableArea) {
        await user.click(clickableArea);
      }

      expect(mockContextValue.fetchFolders).toHaveBeenCalledWith('new-folder', 1);
      expect(mockContextValue.setSelectedFolder).toHaveBeenCalledWith('new-folder');
      expect(mockContextValue.setBases).toHaveBeenCalledWith([]);
      expect(mockContextValue.setSelectedKb).toHaveBeenCalledWith('');
      expect(mockContextValue.fetchBases).toHaveBeenCalledWith('new-folder');
      expect(mockContextValue.fetchDocuments).toHaveBeenCalledWith('new-folder');
      expect(mockToggle).toHaveBeenCalled();
    });

    it('should not reset bases and selectedKb when clicking already selected folder', async () => {
      const user = userEvent.setup();
      const mockToggle = vi.fn();
      const node = createMockNode({
        toggle: mockToggle,
        data: { ...defaultProps.node.data, id: 'selected-folder' },
      });

      // Set selectedFolder to same as node id
      mockContextValue.selectedFolder = 'selected-folder';

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const clickableArea = screen
        .getByTestId('tooltip-with-overflow-text')
        .closest('div')
        ?.querySelector('div[role="button"], div') as HTMLElement;
      if (clickableArea) {
        await user.click(clickableArea);
      }

      expect(mockContextValue.setBases).not.toHaveBeenCalled();
      expect(mockContextValue.setSelectedKb).not.toHaveBeenCalled();
      expect(mockToggle).toHaveBeenCalled();
    });

    it('should not fetch bases and documents when clicking already selected folder', async () => {
      const user = userEvent.setup();
      const mockToggle = vi.fn();
      const node = createMockNode({
        toggle: mockToggle,
        data: { ...defaultProps.node.data, id: 'selected-folder' },
      });

      mockContextValue.selectedFolder = 'selected-folder';

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      const clickableArea = screen
        .getByTestId('tooltip-with-overflow-text')
        .closest('div')
        ?.querySelector('div[role="button"], div') as HTMLElement;
      if (clickableArea) {
        await user.click(clickableArea);
      }

      // fetchFolders and setSelectedFolder should still be called
      expect(mockContextValue.fetchFolders).toHaveBeenCalledWith('selected-folder', 1);
      expect(mockContextValue.setSelectedFolder).toHaveBeenCalledWith('selected-folder');

      // But fetchBases and fetchDocuments should not be called
      expect(mockContextValue.fetchBases).not.toHaveBeenCalled();
      expect(mockContextValue.fetchDocuments).not.toHaveBeenCalled();
    });
  });

  describe('Right Item Rendering', () => {
    it('should render right item when provided', () => {
      const rightItem = vi.fn().mockReturnValue(<div data-testid='right-item'>Right Item</div>);

      renderWithMantine(<TreeLeaf {...defaultProps} rightItem={rightItem} />);

      expect(screen.getByTestId('right-item')).toBeInTheDocument();
      expect(screen.getByText('Right Item')).toBeInTheDocument();
      expect(rightItem).toHaveBeenCalledWith(defaultProps.node);
    });

    it('should not render anything when rightItem is not provided', () => {
      renderWithMantine(<TreeLeaf {...defaultProps} />);

      expect(screen.queryByTestId('right-item')).not.toBeInTheDocument();
    });

    it('should pass correct node to rightItem function', () => {
      const rightItem = vi.fn().mockReturnValue(<div>Right</div>);
      const customNode = createMockNode({ id: 'custom-node' });

      renderWithMantine(<TreeLeaf {...defaultProps} node={customNode} rightItem={rightItem} />);

      expect(rightItem).toHaveBeenCalledWith(customNode);
    });
  });

  describe('Drag Handle', () => {
    it('should call dragHandle with ref when provided', () => {
      const dragHandle = vi.fn();

      renderWithMantine(<TreeLeaf {...defaultProps} dragHandle={dragHandle} />);

      // dragHandle should be called with the DOM element
      expect(dragHandle).toHaveBeenCalled();
      const callArgs = dragHandle.mock.calls[0];
      expect(callArgs[0]).toBeInstanceOf(HTMLElement);
    });

    it('should not call dragHandle when not provided', () => {
      const dragHandle = vi.fn();

      renderWithMantine(<TreeLeaf {...defaultProps} />);

      expect(dragHandle).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility and Layout', () => {
    it('should display translated folder name', () => {
      const node = createMockNode({
        data: { ...defaultProps.node.data, name: 'folder.name.key' },
      });

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      // The t function from useTranslate mock just returns the key
      expect(screen.getByText('folder.name.key')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle node with undefined data gracefully', () => {
      const node = createMockNode({
        data: {
          id: '',
          name: '',
          path: '',
          count: 0,
          childFolderCount: 0,
          childKbCount: 0,
        },
      });

      expect(() => {
        renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);
      }).not.toThrow();
    });

    it('should handle node at maximum depth', () => {
      const node = createMockNode({
        level: 5, // MAX_DEPTH
        data: {
          ...defaultProps.node.data,
          childFolderCount: 3,
          children: undefined,
        },
      });

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      // Should render normally
      expect(screen.getByTestId('tooltip-with-overflow-text')).toBeInTheDocument();
    });

    it('should handle very long folder names', () => {
      const longName = 'A'.repeat(100);
      const node = createMockNode({
        data: { ...defaultProps.node.data, name: longName },
      });

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      expect(screen.getByText(longName)).toBeInTheDocument();
    });

    it('should handle special characters in folder names', () => {
      const specialName = 'Folder @#$%^&*()_+-={}[]|\\:";\'<>?,./`~';
      const node = createMockNode({
        data: { ...defaultProps.node.data, name: specialName },
      });

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      expect(screen.getByText(specialName)).toBeInTheDocument();
    });
  });

  describe('Context Integration', () => {
    it('should use all required context methods', () => {
      const node = createMockNode();

      renderWithMantine(<TreeLeaf {...defaultProps} node={node} />);

      // Verify useKbWidgetContext was called
      expect(useKbWidgetContext).toHaveBeenCalled();
    });

    it('should handle context method failures gracefully', async () => {
      const user = userEvent.setup();
      const contextWithErrors = {
        ...mockContextValue,
        fetchFolders: vi.fn().mockImplementation(() => {
          throw new Error('Network error');
        }),
      };

      (useKbWidgetContext as MockedFunction<typeof useKbWidgetContext>).mockReturnValue(
        contextWithErrors as any
      );

      renderWithMantine(<TreeLeaf {...defaultProps} />);

      const clickableArea = screen
        .getByTestId('tooltip-with-overflow-text')
        .closest('div')
        ?.querySelector('div[role="button"], div') as HTMLElement;

      // Should not throw error even if context methods fail
      expect(async () => {
        if (clickableArea) {
          await user.click(clickableArea);
        }
      }).not.toThrow();
    });
  });
});
