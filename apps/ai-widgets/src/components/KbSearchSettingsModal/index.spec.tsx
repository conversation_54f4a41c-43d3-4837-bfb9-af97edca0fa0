import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { ROOT_FOLDER, ROOT_PATH } from '@/constants/kb';
import type { Folder, Kb, KBDocument, WidgetInstance } from '@/types';
import { renderWithMantine } from '@/utils/test';
import KbSearchSettingsModal from './index';

// Mock contexts
const mockWidgetContext = {
  widgets: [{ id: 'widget-1', type: 'KB_SEARCH', name: 'KB Search Widget' }],
  widgetInstances: [] as WidgetInstance[],
  installWidgetInstance: vi.fn(),
  updateWidgetInstance: vi.fn(),
  updateWidgetInstanceSettings: vi.fn(),
};

const mockAppContext = {
  openConfirmModal: vi.fn(),
};

const mockKbWidgetContext = {
  allBases: [] as Kb[],
  allDocuments: [] as KBDocument[],
  cleanup: vi.fn(),
  deletedFolderIds: [] as string[],
  deletedKbIds: [] as string[],
  fetchBases: vi.fn(),
  fetchDocuments: vi.fn(),
  fetchFolderByIds: vi.fn(),
  fetchFolders: vi.fn(),
  fetchKbsByIds: vi.fn(),
  fetchDocumentsByIds: vi.fn(),
  folders: [] as Folder[],
  loading: false,
  searchCombineFolderAndKb: vi.fn(),
  selectedFolders: [] as Folder[],
  selectedKbs: [] as Kb[],
  selectedDocuments: [] as KBDocument[],
  setSelectedFolders: vi.fn(),
  setSelectedKbs: vi.fn(),
  setSelectedDocuments: vi.fn(),
};

// Mock UI components
vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} data-testid='deca-button' {...props}>
      {children}
    </button>
  ),
  SearchInput: ({ onChange, onClear, placeholder, ...props }: any) => (
    <input
      type='text'
      placeholder={placeholder}
      onChange={(e) => onChange?.(e.target.value)}
      data-testid='search-input'
      {...props}
    />
  ),
}));

vi.mock('@resola-ai/ui/components/ConfirmModal', () => ({
  default: ({
    children,
    onConfirm,
    onClose,
    title,
    cancelText,
    confirmText,
    confirmButtonProps,
    cancelButtonProps,
    ...props
  }: any) => (
    <div data-testid='confirm-modal' {...props}>
      <h2 data-testid='modal-title'>{title}</h2>
      <div data-testid='modal-content'>{children}</div>
      <button onClick={onClose} data-testid='cancel-button' {...cancelButtonProps}>
        {cancelText}
      </button>
      <button onClick={onConfirm} data-testid='confirm-button' {...confirmButtonProps}>
        {confirmText}
      </button>
    </div>
  ),
}));

// Mock context hooks
vi.mock('@/contexts/WidgetContext', () => ({
  useWidgetContext: () => mockWidgetContext,
  WidgetContextProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => mockAppContext,
  AppContextProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock('@/contexts/KbWidgetContext', () => ({
  KbWidgetContextProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useKbWidgetContext: () => mockKbWidgetContext,
}));

// Mock field components
vi.mock('../DefaultSettingsModal/Fields', () => ({
  DescriptionField: ({ control }: { control: any }) => {
    const { register } = control ? { register: control.register } : { register: () => ({}) };
    return (
      <input
        data-testid='description-field'
        placeholder='Description'
        {...register('description')}
      />
    );
  },
  NameField: ({ control }: { control: any }) => {
    const { register } = control ? { register: control.register } : { register: () => ({}) };
    return (
      <input
        data-testid='name-field'
        placeholder='Name'
        required
        {...register('name', { required: true })}
      />
    );
  },
  StatusField: ({ control }: { control: any }) => {
    const { register } = control ? { register: control.register } : { register: () => ({}) };
    return (
      <select data-testid='status-field' {...register('status')}>
        <option value='public'>Public</option>
        <option value='private'>Private</option>
      </select>
    );
  },
}));

// Mock other components
vi.mock('./SearchFolderAndKb', () => ({
  default: () => <div data-testid='search-folder-and-kb'>Search Folder and KB</div>,
}));

vi.mock('./SelectFolderAndKb', () => ({
  default: () => <div data-testid='select-folder-and-kb'>Select Folder and KB</div>,
}));

vi.mock('./SelectedFolderAndKb', () => ({
  default: () => <div data-testid='selected-folder-and-kb'>Selected Folder and KB</div>,
}));

vi.mock('./KbFolderDeletedError', () => ({
  default: ({ show }: { show: boolean }) =>
    show ? <div data-testid='kb-folder-deleted-error'>Deleted folders detected</div> : null,
}));

// Mock utility functions
vi.mock('@/utils/kb', () => ({
  getSelectedKbAndFolderName: vi.fn(() => 'Selected folders and KBs'),
}));

// Mock lodash debounce
vi.mock('lodash/debounce', () => ({
  default: (fn: any) => fn,
}));

const mockFolder: Folder = {
  id: 'folder-1',
  name: 'Test Folder',
  path: '/test-folder',
  count: 5,
  childFolderCount: 2,
  childKbCount: 3,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockKb: Kb = {
  id: 'kb-1',
  name: 'Test Knowledge Base',
  description: 'Test KB Description',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
};

const mockDocument: KBDocument = {
  id: 'doc-1',
  type: 'document',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  metadata: {
    name: 'Test Document',
    contentType: 'application/pdf',
    contentLength: 1024,
    uploadUrl: 'https://example.com/upload',
    downloadUrl: 'https://example.com/download',
    uploadStatus: 'completed',
    downloadUrlExpires: null,
    createdBy: 'user-1',
    previewText: 'Preview text',
    documentContentPreview: 'Document preview',
  },
};

const mockSettings: WidgetInstance = {
  id: 'instance-1',
  name: 'Test KB Search',
  description: 'Test description',
  status: 'public',
  created: new Date(),
  updated: new Date(),
  orgId: 'org-1',
  widgetId: 'widget-1',
  widgetType: 'KB_SEARCH',
  widget: {
    id: 'widget-1',
    name: 'KB Search Widget',
    description: 'KB Search Widget Description',
    compilations: [],
    settings: {
      isAI: false,
      maxInstances: 1,
      type: 'singleton',
    },
  },
  settings: {
    searchFolders: [ROOT_PATH],
    searchBases: ['kb-1'],
    searchDocuments: ['doc-1'],
  },
};

describe('KbSearchSettingsModal', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockKbWidgetContext.selectedFolders = [];
    mockKbWidgetContext.selectedKbs = [];
    mockKbWidgetContext.selectedDocuments = [];
    mockKbWidgetContext.folders = [];
    mockKbWidgetContext.allBases = [];
    mockKbWidgetContext.allDocuments = [];
    mockKbWidgetContext.deletedFolderIds = [];
    mockKbWidgetContext.deletedKbIds = [];
    mockKbWidgetContext.loading = false;
  });

  it('should render the modal with initial state', () => {
    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={vi.fn()} />);

    expect(screen.getByTestId('confirm-modal')).toBeInTheDocument();
    expect(screen.getByTestId('modal-title')).toHaveTextContent('kb.title');
    expect(screen.getByTestId('name-field')).toBeInTheDocument();
    expect(screen.getByTestId('description-field')).toBeInTheDocument();
    expect(screen.getByTestId('status-field')).toBeInTheDocument();
  });

  it('should render with existing settings', () => {
    mockKbWidgetContext.selectedFolders = [ROOT_FOLDER];
    mockKbWidgetContext.selectedKbs = [mockKb];
    mockKbWidgetContext.selectedDocuments = [mockDocument];

    renderWithMantine(
      <KbSearchSettingsModal opened={true} onClose={vi.fn()} settings={mockSettings} />
    );

    expect(mockKbWidgetContext.fetchKbsByIds).toHaveBeenCalledWith(['kb-1']);
    expect(mockKbWidgetContext.fetchFolderByIds).not.toHaveBeenCalled();
    expect(mockKbWidgetContext.fetchDocumentsByIds).toHaveBeenCalledWith(['doc-1']);
  });

  it('should switch to folder selection mode when change button is clicked', async () => {
    const user = userEvent.setup();

    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={vi.fn()} />);

    const changeButton = screen.getByTestId('deca-button');
    await user.click(changeButton);

    expect(screen.getByTestId('modal-title')).toHaveTextContent('kb.selectFolderAndKb');
    expect(screen.getByTestId('search-input')).toBeInTheDocument();
    expect(screen.getByTestId('select-folder-and-kb')).toBeInTheDocument();
    expect(screen.getByTestId('selected-folder-and-kb')).toBeInTheDocument();
  });

  it('should perform search when typing in search input', async () => {
    const user = userEvent.setup();

    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={vi.fn()} />);

    // Switch to folder selection mode
    const changeButton = screen.getByTestId('deca-button');
    await user.click(changeButton);

    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'test search');

    expect(mockKbWidgetContext.searchCombineFolderAndKb).toHaveBeenCalledWith('test search');
    expect(screen.getByTestId('search-folder-and-kb')).toBeInTheDocument();
  });

  it('should handle form submission for new widget instance', async () => {
    const user = userEvent.setup();
    const onCloseMock = vi.fn();

    mockWidgetContext.installWidgetInstance.mockResolvedValue({});

    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={onCloseMock} />);

    // Fill in required name field
    const nameField = screen.getByTestId('name-field');
    await user.type(nameField, 'Test Widget Name');

    const confirmButton = screen.getByTestId('confirm-button');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockWidgetContext.installWidgetInstance).toHaveBeenCalled();
      expect(onCloseMock).toHaveBeenCalled();
      expect(mockAppContext.openConfirmModal).toHaveBeenCalledWith({
        content: 'modal.success.settings.saved',
        cancelText: 'common.button.close',
        options: { isShowConfirm: false },
      });
    });
  });

  it('should handle form submission for existing widget instance', async () => {
    const user = userEvent.setup();
    const onCloseMock = vi.fn();

    mockWidgetContext.updateWidgetInstance.mockResolvedValue({});
    mockWidgetContext.updateWidgetInstanceSettings.mockResolvedValue({});

    renderWithMantine(
      <KbSearchSettingsModal opened={true} onClose={onCloseMock} settings={mockSettings} />
    );

    const confirmButton = screen.getByTestId('confirm-button');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockWidgetContext.updateWidgetInstance).toHaveBeenCalledWith(
        mockSettings.id,
        expect.any(Object)
      );
      expect(mockWidgetContext.updateWidgetInstanceSettings).toHaveBeenCalledWith(
        mockSettings.id,
        expect.any(Object)
      );
      expect(onCloseMock).toHaveBeenCalled();
    });
  });

  it('should handle duplicated error on form submission', async () => {
    const user = userEvent.setup();
    const onCloseMock = vi.fn();

    mockWidgetContext.installWidgetInstance.mockRejectedValue(new Error('Duplicated'));

    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={onCloseMock} />);

    // Fill in required name field
    const nameField = screen.getByTestId('name-field');
    await user.type(nameField, 'Test Widget Name');

    const confirmButton = screen.getByTestId('confirm-button');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockAppContext.openConfirmModal).toHaveBeenCalledWith({
        content: 'kb.error.duplicated',
        cancelText: 'common.button.close',
        options: { isShowConfirm: false },
      });
    });
  });

  it('should handle limited error on form submission', async () => {
    const user = userEvent.setup();
    const onCloseMock = vi.fn();

    mockWidgetContext.installWidgetInstance.mockRejectedValue(new Error('Limited'));

    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={onCloseMock} />);

    // Fill in required name field
    const nameField = screen.getByTestId('name-field');
    await user.type(nameField, 'Test Widget Name');

    const confirmButton = screen.getByTestId('confirm-button');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockAppContext.openConfirmModal).toHaveBeenCalledWith({
        content: 'kb.error.limited',
        cancelText: 'common.button.close',
        options: { isShowConfirm: false },
      });
    });
  });

  it('should show deleted folder error when there are deleted items', () => {
    mockKbWidgetContext.deletedFolderIds = ['deleted-folder-1'];
    mockKbWidgetContext.deletedKbIds = ['deleted-kb-1'];

    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={vi.fn()} />);

    expect(screen.getByTestId('kb-folder-deleted-error')).toBeInTheDocument();
  });

  it('should handle modal close properly', async () => {
    const user = userEvent.setup();
    const onCloseMock = vi.fn();

    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={onCloseMock} />);

    const cancelButton = screen.getByTestId('cancel-button');
    await user.click(cancelButton);

    expect(onCloseMock).toHaveBeenCalled();

    await waitFor(() => {
      expect(mockKbWidgetContext.cleanup).toHaveBeenCalled();
    });
  });

  it('should handle back navigation from folder selection mode', async () => {
    const user = userEvent.setup();
    const onCloseMock = vi.fn();

    mockKbWidgetContext.folders = [mockFolder];
    mockKbWidgetContext.allBases = [mockKb];
    mockKbWidgetContext.allDocuments = [mockDocument];
    mockWidgetContext.widgetInstances = [mockSettings];

    renderWithMantine(
      <KbSearchSettingsModal opened={true} onClose={onCloseMock} settings={mockSettings} />
    );

    // Switch to folder selection mode
    const changeButton = screen.getByTestId('deca-button');
    await user.click(changeButton);

    // Click cancel to go back
    const cancelButton = screen.getByTestId('cancel-button');
    await user.click(cancelButton);

    expect(screen.getByTestId('modal-title')).toHaveTextContent('kb.title');
    expect(mockKbWidgetContext.setSelectedFolders).toHaveBeenCalled();
    expect(mockKbWidgetContext.setSelectedKbs).toHaveBeenCalled();
    expect(mockKbWidgetContext.setSelectedDocuments).toHaveBeenCalled();
  });

  it('should disable confirm button when required fields are missing', () => {
    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={vi.fn()} />);

    const confirmButton = screen.getByTestId('confirm-button');
    expect(confirmButton).toBeDisabled();
  });

  it('should disable confirm button in folder mode when no items selected', async () => {
    const user = userEvent.setup();

    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={vi.fn()} />);

    // Switch to folder selection mode
    const changeButton = screen.getByTestId('deca-button');
    await user.click(changeButton);

    const confirmButton = screen.getByTestId('confirm-button');
    expect(confirmButton).toBeDisabled();
  });

  it('should call fetchFolders, fetchBases, and fetchDocuments on mount', () => {
    renderWithMantine(<KbSearchSettingsModal opened={true} onClose={vi.fn()} />);

    expect(mockKbWidgetContext.fetchFolders).toHaveBeenCalledWith(ROOT_PATH, 1);
    expect(mockKbWidgetContext.fetchBases).toHaveBeenCalled();
    expect(mockKbWidgetContext.fetchDocuments).toHaveBeenCalledWith(ROOT_PATH);
  });

  it('should update form values when selected items change', async () => {
    const { rerender } = renderWithMantine(
      <KbSearchSettingsModal opened={true} onClose={vi.fn()} />
    );

    // Simulate context changes
    mockKbWidgetContext.selectedFolders = [mockFolder];
    mockKbWidgetContext.selectedKbs = [mockKb];
    mockKbWidgetContext.selectedDocuments = [mockDocument];

    rerender(<KbSearchSettingsModal opened={true} onClose={vi.fn()} />);

    // The setValue calls would have been made through the useEffect hooks
    // This tests the reactive behavior of the component
    expect(screen.getByTestId('confirm-modal')).toBeInTheDocument();
  });
});
