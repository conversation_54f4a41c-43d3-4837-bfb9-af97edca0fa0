import type { Kb, KBDocument } from '@/types';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the SVG imports
vi.mock('@/assets/icons/pdf.svg', () => ({ default: 'mocked-pdf-icon.svg' }));
vi.mock('@/assets/icons/txt.svg', () => ({ default: 'mocked-txt-icon.svg' }));
vi.mock('@/assets/icons/csv.svg', () => ({ default: 'mocked-csv-icon.svg' }));
vi.mock('@/assets/icons/doc.svg', () => ({ default: 'mocked-doc-icon.svg' }));
vi.mock('@/assets/icons/docx.svg', () => ({ default: 'mocked-docx-icon.svg' }));
vi.mock('@/assets/icons/md.svg', () => ({ default: 'mocked-md-icon.svg' }));

// Mock the Colors from theme configuration
vi.mock('@resola-ai/ui/constants/themeConfiguration', () => ({
  Colors: {
    decaBlue: {
      5: '#1c7ed6',
    },
  },
}));

// Mock Tabler icons
vi.mock('@tabler/icons-react', () => ({
  IconFileDescription: ({ color, ...props }: any) => (
    <svg data-testid='icon-file-description' data-color={color} {...props}>
      <title>File Description Icon</title>
    </svg>
  ),
  IconArticle: ({ color, ...props }: any) => (
    <svg data-testid='icon-article' data-color={color} {...props}>
      <title>Article Icon</title>
    </svg>
  ),
}));

// Re-import the module to get the updated mocked version
const { renderIcon: renderIconFunction, FileTypeIconMapping: FileTypeIconMappingConstant } =
  await import('./index');

describe('FileTypeIcons', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('FileTypeIconMapping', () => {
    it('should have correct mappings for all content types', () => {
      const expectedMappings = {
        'application/pdf': 'pdf',
        'text/plain': 'txt',
        'text/csv': 'csv',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'doc',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'docx',
        'text/markdown': 'md',
      };

      Object.entries(expectedMappings).forEach(([contentType, expectedType]) => {
        const iconComponent =
          FileTypeIconMappingConstant[contentType as keyof typeof FileTypeIconMappingConstant];
        expect(iconComponent).toBeDefined();

        // Render the component to test its structure
        const { container } = render(iconComponent as React.ReactElement);
        const img = container.querySelector('img');
        expect(img).toBeInTheDocument();
        expect(img?.getAttribute('alt')).toBe(expectedType);
        expect(img?.getAttribute('src')).toContain(`mocked-${expectedType}-icon.svg`);
      });
    });
  });

  describe('renderIcon', () => {
    it('should render Article icon for article baseType', () => {
      const articleKb: Kb = {
        id: '1',
        name: 'Test Article',
        description: 'Test Description',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        baseType: 'article',
      };

      const result = renderIconFunction(articleKb);
      render(result as React.ReactElement);

      expect(screen.getByTestId('icon-article')).toBeInTheDocument();
      expect(screen.getByTestId('icon-article')).toHaveAttribute('data-color', '#1c7ed6');
    });

    it('should render correct icon for PDF document', () => {
      const pdfDocument: KBDocument = {
        id: '1',
        type: 'document',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        metadata: {
          name: 'test-file.pdf',
          contentType: 'application/pdf',
          contentLength: 1000,
          uploadUrl: 'https://example.com/upload',
          downloadUrl: 'https://example.com/download',
          uploadStatus: 'completed',
          downloadUrlExpires: null,
          createdBy: 'user1',
          previewText: 'Preview text',
          documentContentPreview: 'Content preview',
        },
      };

      const result = renderIconFunction(pdfDocument);
      const { container } = render(result as React.ReactElement);

      const img = container.querySelector('img');
      expect(img).toBeInTheDocument();
      expect(img?.getAttribute('alt')).toBe('pdf');
      expect(img?.getAttribute('src')).toContain('mocked-pdf-icon.svg');
    });

    it('should render correct icon for different document types', () => {
      const testCases = [
        { extension: 'txt', expectedAlt: 'txt', expectedSrc: 'mocked-txt-icon.svg' },
        { extension: 'csv', expectedAlt: 'csv', expectedSrc: 'mocked-csv-icon.svg' },
        { extension: 'doc', expectedAlt: 'doc', expectedSrc: 'mocked-doc-icon.svg' },
        { extension: 'docx', expectedAlt: 'docx', expectedSrc: 'mocked-docx-icon.svg' },
        { extension: 'md', expectedAlt: 'md', expectedSrc: 'mocked-md-icon.svg' },
      ];

      testCases.forEach(({ extension, expectedAlt, expectedSrc }) => {
        const document: KBDocument = {
          id: '1',
          type: 'document',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
          metadata: {
            name: `test-file.${extension}`,
            contentType: 'application/octet-stream',
            contentLength: 1000,
            uploadUrl: 'https://example.com/upload',
            downloadUrl: 'https://example.com/download',
            uploadStatus: 'completed',
            downloadUrlExpires: null,
            createdBy: 'user1',
            previewText: 'Preview text',
            documentContentPreview: 'Content preview',
          },
        };

        const result = renderIconFunction(document);
        const { container } = render(result as React.ReactElement);

        const img = container.querySelector('img');
        expect(img).toBeInTheDocument();
        expect(img?.getAttribute('alt')).toBe(expectedAlt);
        expect(img?.getAttribute('src')).toContain(expectedSrc);
      });
    });

    it('should render fallback icon for document with unknown extension', () => {
      const unknownDocument: KBDocument = {
        id: '1',
        type: 'document',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        metadata: {
          name: 'test-file.unknown',
          contentType: 'application/octet-stream',
          contentLength: 1000,
          uploadUrl: 'https://example.com/upload',
          downloadUrl: 'https://example.com/download',
          uploadStatus: 'completed',
          downloadUrlExpires: null,
          createdBy: 'user1',
          previewText: 'Preview text',
          documentContentPreview: 'Content preview',
        },
      };

      const result = renderIconFunction(unknownDocument);
      render(result as React.ReactElement);

      expect(screen.getByTestId('icon-file-description')).toBeInTheDocument();
      expect(screen.getByTestId('icon-file-description')).toHaveAttribute('data-color', '#1c7ed6');
    });

    it('should render fallback icon for document without extension', () => {
      const noExtensionDocument: KBDocument = {
        id: '1',
        type: 'document',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        metadata: {
          name: 'test-file',
          contentType: 'application/octet-stream',
          contentLength: 1000,
          uploadUrl: 'https://example.com/upload',
          downloadUrl: 'https://example.com/download',
          uploadStatus: 'completed',
          downloadUrlExpires: null,
          createdBy: 'user1',
          previewText: 'Preview text',
          documentContentPreview: 'Content preview',
        },
      };

      const result = renderIconFunction(noExtensionDocument);
      render(result as React.ReactElement);

      expect(screen.getByTestId('icon-file-description')).toBeInTheDocument();
      expect(screen.getByTestId('icon-file-description')).toHaveAttribute('data-color', '#1c7ed6');
    });

    it('should render fallback icon for Kb object (no metadata)', () => {
      const kb: Kb = {
        id: '1',
        name: 'Test KB',
        description: 'Test Description',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      };

      const result = renderIconFunction(kb);
      render(result as React.ReactElement);

      expect(screen.getByTestId('icon-file-description')).toBeInTheDocument();
      expect(screen.getByTestId('icon-file-description')).toHaveAttribute('data-color', '#1c7ed6');
    });

    it('should render fallback icon for document baseType that is not article', () => {
      const documentKb: Kb = {
        id: '1',
        name: 'Test Document',
        description: 'Test Description',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        baseType: 'document',
      };

      const result = renderIconFunction(documentKb);
      render(result as React.ReactElement);

      expect(screen.getByTestId('icon-file-description')).toBeInTheDocument();
      expect(screen.getByTestId('icon-file-description')).toHaveAttribute('data-color', '#1c7ed6');
    });

    it('should handle document with empty filename gracefully', () => {
      const emptyNameDocument: KBDocument = {
        id: '1',
        type: 'document',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        metadata: {
          name: '',
          contentType: 'application/pdf',
          contentLength: 1000,
          uploadUrl: 'https://example.com/upload',
          downloadUrl: 'https://example.com/download',
          uploadStatus: 'completed',
          downloadUrlExpires: null,
          createdBy: 'user1',
          previewText: 'Preview text',
          documentContentPreview: 'Content preview',
        },
      };

      const result = renderIconFunction(emptyNameDocument);
      render(result as React.ReactElement);

      expect(screen.getByTestId('icon-file-description')).toBeInTheDocument();
      expect(screen.getByTestId('icon-file-description')).toHaveAttribute('data-color', '#1c7ed6');
    });

    it('should handle complex filename with multiple dots correctly', () => {
      const complexNameDocument: KBDocument = {
        id: '1',
        type: 'document',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        metadata: {
          name: 'my.complex.file.name.pdf',
          contentType: 'application/pdf',
          contentLength: 1000,
          uploadUrl: 'https://example.com/upload',
          downloadUrl: 'https://example.com/download',
          uploadStatus: 'completed',
          downloadUrlExpires: null,
          createdBy: 'user1',
          previewText: 'Preview text',
          documentContentPreview: 'Content preview',
        },
      };

      const result = renderIconFunction(complexNameDocument);
      const { container } = render(result as React.ReactElement);

      const img = container.querySelector('img');
      expect(img).toBeInTheDocument();
      expect(img?.getAttribute('alt')).toBe('pdf');
      expect(img?.getAttribute('src')).toContain('mocked-pdf-icon.svg');
    });
  });
});
