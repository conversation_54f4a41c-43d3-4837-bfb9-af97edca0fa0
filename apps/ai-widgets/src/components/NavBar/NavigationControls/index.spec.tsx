import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { mockLibraries, renderWithMantine } from '@/utils/test';
import NavigationControls from './index';

// Mock the dependencies
vi.mock('@/configs', () => ({
  AppConfig: {
    BASE_PATH: '/ai-widgets',
  },
}));

// Mock react-router-dom with hoisted functions
const { mockLocation } = vi.hoisted(() => ({
  mockLocation: { pathname: '/ai-widgets' },
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useLocation: () => mockLocation,
    NavLink: ({ children, to, ...props }: any) => (
      <a href={to} data-testid={`nav-link-${to}`} {...props}>
        {children}
      </a>
    ),
  };
});

// Mock @resola-ai/ui/hooks
const { mockCreatePathWithLngParam } = vi.hoisted(() => ({
  mockCreatePathWithLngParam: vi.fn((path: string) => `${path}?lang=en`),
}));

vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({
    createPathWithLngParam: mockCreatePathWithLngParam,
  }),
}));

// Mock lodash cloneDeep
vi.mock('lodash/cloneDeep', () => ({
  default: (obj: any) => JSON.parse(JSON.stringify(obj)),
}));

describe('NavigationControls', () => {
  mockLibraries();

  beforeEach(() => {
    vi.clearAllMocks();
    mockLocation.pathname = '/ai-widgets';
  });

  it('renders correctly with default navigation items', () => {
    renderWithMantine(<NavigationControls />);

    // Check if the Stack container is rendered
    expect(screen.getByRole('link')).toBeInTheDocument();

    // Check if the ActionIcon is rendered
    expect(screen.getByRole('button')).toBeInTheDocument();

    // Check if the icon is rendered (IconLayoutGridAdd)
    const actionIcon = screen.getByRole('button');
    expect(actionIcon).toBeInTheDocument();
  });

  it('applies active class when path matches navigation item', () => {
    mockLocation.pathname = '/ai-widgets';

    renderWithMantine(<NavigationControls />);

    const actionIcon = screen.getByRole('button');
    expect(actionIcon).toHaveClass('mantine-ActionIcon-root');
  });

  it('applies inactive class when path does not match navigation item', () => {
    mockLocation.pathname = '/ai-widgets/other-path';

    renderWithMantine(<NavigationControls />);

    const actionIcon = screen.getByRole('button');
    expect(actionIcon).toHaveClass('mantine-ActionIcon-root');
  });

  it('calls createPathWithLngParam with correct path for navigation links', () => {
    renderWithMantine(<NavigationControls />);

    // Check if createPathWithLngParam is called with the correct base path
    expect(mockCreatePathWithLngParam).toHaveBeenCalledWith('/ai-widgets');
  });

  it('renders NavLink with correct href generated by createPathWithLngParam', () => {
    mockCreatePathWithLngParam.mockReturnValue('/ai-widgets?lang=en');

    renderWithMantine(<NavigationControls />);

    const navLink = screen.getByTestId('nav-link-/ai-widgets?lang=en');
    expect(navLink).toBeInTheDocument();
    expect(navLink).toHaveAttribute('href', '/ai-widgets?lang=en');
  });

  it('updates active path when location pathname changes', () => {
    const { rerender } = renderWithMantine(<NavigationControls />);

    // Initially should be active for base path
    let actionIcon = screen.getByRole('button');
    expect(actionIcon).toBeInTheDocument();

    // Change the pathname
    mockLocation.pathname = '/ai-widgets/detail';

    rerender(<NavigationControls />);

    actionIcon = screen.getByRole('button');
    expect(actionIcon).toBeInTheDocument();
  });

  it('handles navigation items correctly with proper key and structure', () => {
    renderWithMantine(<NavigationControls />);

    // Check if navigation link has the correct key structure
    const navLink = screen.getByRole('link');
    expect(navLink).toBeInTheDocument();

    // Check if Group component is rendered inside NavLink
    const group = navLink.querySelector('.mantine-Group-root');
    expect(group).toBeInTheDocument();

    // Check if ActionIcon is rendered inside Group
    const actionIcon = screen.getByRole('button');
    expect(actionIcon).toBeInTheDocument();
  });

  it('renders with correct Stack alignment and gap', () => {
    renderWithMantine(<NavigationControls />);

    // Check if Stack has correct classes/structure
    const stackElement = screen.getByRole('link').closest('.mantine-Stack-root');
    expect(stackElement).toBeInTheDocument();
  });

  it('creates navigation items with correct active state logic', () => {
    // Test when path includes the navigation item path
    mockLocation.pathname = '/ai-widgets/some-sub-path';

    renderWithMantine(<NavigationControls />);

    const actionIcon = screen.getByRole('button');
    expect(actionIcon).toBeInTheDocument();
  });

  it('renders correct number of navigation items', () => {
    renderWithMantine(<NavigationControls />);

    // Should render exactly one navigation item (based on defaultNavigationItems)
    const navLinks = screen.getAllByRole('link');
    expect(navLinks).toHaveLength(1);

    const actionIcons = screen.getAllByRole('button');
    expect(actionIcons).toHaveLength(1);
  });

  it('handles edge case when activePath is empty', () => {
    mockLocation.pathname = '';

    renderWithMantine(<NavigationControls />);

    const actionIcon = screen.getByRole('button');
    expect(actionIcon).toBeInTheDocument();
  });

  it('correctly identifies active navigation item using reverse find logic', () => {
    // Test the logic where it finds the active nav using reverse and includes
    mockLocation.pathname = '/ai-widgets';

    renderWithMantine(<NavigationControls />);

    // The component should find the matching navigation item
    const actionIcon = screen.getByRole('button');
    expect(actionIcon).toBeInTheDocument();
  });

  it('handles user interaction with navigation links', async () => {
    const user = userEvent.setup();

    renderWithMantine(<NavigationControls />);

    const navLink = screen.getByRole('link');

    // Simulate user click on navigation link
    await user.click(navLink);

    // The href should be set correctly
    expect(navLink).toHaveAttribute('href', '/ai-widgets?lang=en');
  });

  it('maintains correct component structure with Group and ActionIcon', () => {
    renderWithMantine(<NavigationControls />);

    // Check the complete structure: Stack > NavLink > Group > ActionIcon
    const navLink = screen.getByRole('link');
    const group = navLink.querySelector('.mantine-Group-root');
    const actionIcon = screen.getByRole('button');

    expect(navLink).toBeInTheDocument();
    expect(group).toBeInTheDocument();
    expect(actionIcon).toBeInTheDocument();

    // Check that ActionIcon is inside Group
    expect(group).toContainElement(actionIcon);
  });
});
