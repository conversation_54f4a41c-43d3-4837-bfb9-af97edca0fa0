import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { mockLibraries, renderWithMantine } from '@/utils/test';
import NavbarContainer from './index';

// Mock the NavigationControls component
vi.mock('./NavigationControls', () => ({
  default: () => <div data-testid='navigation-controls'>NavigationControls</div>,
}));

describe('NavbarContainer', () => {
  mockLibraries();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with proper structure', () => {
    renderWithMantine(<NavbarContainer />);

    // Check if the main Stack container is rendered by targeting it more specifically
    const stackElement = screen.getByTestId('navigation-controls').parentElement;
    expect(stackElement).toBeInTheDocument();
    expect(stackElement).toHaveClass('mantine-Stack-root');
  });

  it('renders NavigationControls component', () => {
    renderWithMantine(<NavbarContainer />);

    // Check if NavigationControls is rendered inside the navbar
    const navigationControls = screen.getByTestId('navigation-controls');
    expect(navigationControls).toBeInTheDocument();
    expect(navigationControls).toHaveTextContent('NavigationControls');
  });

  it('applies correct CSS classes from useStyles', () => {
    renderWithMantine(<NavbarContainer />);

    const stackElement = screen.getByTestId('navigation-controls').parentElement;

    // Check if the navbarContainer styles are applied
    expect(stackElement).toHaveClass('mantine-Stack-root');

    // The component should have the custom class from createStyles
    expect(stackElement?.className).toMatch(/css-/);
  });

  it('maintains proper component hierarchy', () => {
    renderWithMantine(<NavbarContainer />);

    const navigationControls = screen.getByTestId('navigation-controls');
    const stackElement = navigationControls.parentElement;

    // Check that NavigationControls is inside the Stack
    expect(stackElement).toContainElement(navigationControls);
  });

  it('applies the navbarContainer class to Stack component', () => {
    renderWithMantine(<NavbarContainer />);

    const stackElement = screen.getByTestId('navigation-controls').parentElement;

    // Check that the Stack has the expected CSS classes
    expect(stackElement).toHaveClass('mantine-Stack-root');
    // The emotion class should be present (indicating useStyles worked)
    expect(stackElement?.getAttribute('class')).toMatch(/css-/);
  });

  it('renders without crashing when NavigationControls is present', () => {
    expect(() => {
      renderWithMantine(<NavbarContainer />);
    }).not.toThrow();

    // Verify the component tree is complete
    const navigationControls = screen.getByTestId('navigation-controls');
    const stackElement = navigationControls.parentElement;

    expect(stackElement).toBeInTheDocument();
    expect(navigationControls).toBeInTheDocument();
  });

  it('has correct default export', () => {
    // Test that the component is exported as default
    expect(NavbarContainer).toBeDefined();
    expect(typeof NavbarContainer).toBe('function');
  });

  it('creates styles with useStyles hook', () => {
    renderWithMantine(<NavbarContainer />);

    const stackElement = screen.getByTestId('navigation-controls').parentElement;

    // Verify that emotion styles are applied (emotion classes are present)
    const classNames = stackElement?.getAttribute('class') || '';
    expect(classNames).toMatch(/css-/);
  });

  it('renders Stack with single child NavigationControls', () => {
    renderWithMantine(<NavbarContainer />);

    const navigationControls = screen.getByTestId('navigation-controls');
    const stackElement = navigationControls.parentElement;

    // Check that Stack has exactly one child
    expect(stackElement?.children).toHaveLength(1);
    expect(stackElement?.children[0]).toBe(navigationControls);
  });

  it('applies mantine Stack component correctly', () => {
    renderWithMantine(<NavbarContainer />);

    const stackElement = screen.getByTestId('navigation-controls').parentElement;

    // Verify it's rendered as a Mantine Stack
    expect(stackElement).toHaveClass('mantine-Stack-root');
    expect(stackElement?.tagName).toBe('DIV');
  });
});
