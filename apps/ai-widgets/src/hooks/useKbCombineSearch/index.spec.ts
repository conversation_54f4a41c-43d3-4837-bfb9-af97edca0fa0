import { renderHook } from '@testing-library/react';
import useS<PERSON> from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { KB_COMBINED_PATH } from '@/constants/kb';
import { KbsAPI } from '@/services/api/kbs';
import type { SearchCombineResult } from '@/types';
import type { ISuccessListResponseWithoutPagination } from '@resola-ai/models';
import { useKbCombineSearch } from './index';

// Mock the external dependencies
vi.mock('swr');
vi.mock('@/services/api/kbs', () => ({
  KbsAPI: {
    combineSearch: vi.fn(),
  },
}));

describe('useKbCombineSearch', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should call useSWR with correct key and fetcher when query is provided', () => {
    const mockUseSWR = vi.mocked(useSWR);
    const query = 'test query';

    renderHook(() => useKbCombineSearch(query));

    expect(mockUseSWR).toHaveBeenCalledWith(
      [`${KB_COMBINED_PATH}/search`, query],
      expect.any(Function),
      { revalidateOnFocus: false }
    );
  });

  it('should call useSWR with false key when query is empty', () => {
    const mockUseSWR = vi.mocked(useSWR);

    renderHook(() => useKbCombineSearch(''));

    expect(mockUseSWR).toHaveBeenCalledWith(false, expect.any(Function), {
      revalidateOnFocus: false,
    });
  });

  it('should call useSWR with false key when query is falsy', () => {
    const mockUseSWR = vi.mocked(useSWR);

    renderHook(() => useKbCombineSearch(null as any));

    expect(mockUseSWR).toHaveBeenCalledWith(false, expect.any(Function), {
      revalidateOnFocus: false,
    });
  });

  it('should call KbsAPI.combineSearch with correct query when fetcher is executed', () => {
    const mockUseSWR = vi.mocked(useSWR);
    const mockCombineSearch = vi.mocked(KbsAPI.combineSearch);
    const query = 'test query';

    renderHook(() => useKbCombineSearch(query));

    // Get the fetcher function from the useSWR call
    const fetcherFunction = mockUseSWR.mock.calls[0][1];

    // Execute the fetcher
    fetcherFunction?.();

    expect(mockCombineSearch).toHaveBeenCalledWith(query);
  });

  it('should return search results when API call is successful', () => {
    const mockSearchResults: SearchCombineResult[] = [
      {
        type: 'folder',
        data: {
          id: '1',
          name: 'Test Folder',
          path: '/test-folder',
          parentDirId: '/root',
          count: 5,
          childFolderCount: 2,
          childKbCount: 3,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
      {
        type: 'base',
        data: {
          id: '2',
          name: 'Test Knowledge Base',
          description: 'Test description',
          parentDirId: '/root',
          path: '/test-folder',
          count: 5,
          childFolderCount: 2,
          childKbCount: 3,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
      {
        type: 'document',
        data: {
          id: '3',
          parentDirId: '/root',
          path: '/test-folder',
          count: 5,
          childFolderCount: 2,
          childKbCount: 3,
          name: 'Test Document',
          metadata: {
            name: 'Test Document',
            contentType: 'application/pdf',
            contentLength: 1024,
            uploadUrl: 'https://example.com/upload',
            downloadUrl: 'https://example.com/download',
            uploadStatus: 'completed',
            downloadUrlExpires: new Date().toISOString(),
            createdBy: '<EMAIL>',
            previewText: 'Test preview text',
            documentContentPreview: 'Test content preview',
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
    ];

    const mockResponseData = { data: mockSearchResults };

    vi.mocked(useSWR).mockReturnValue({
      data: mockResponseData,
      error: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
    });

    const { result } = renderHook(() => useKbCombineSearch('test query'));

    expect(result.current.data).toEqual(mockResponseData);
    expect(result.current.error).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
  });

  it('should handle API errors', () => {
    const mockError = new Error('Failed to search knowledge base');

    vi.mocked(useSWR).mockReturnValue({
      data: undefined,
      error: mockError,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
    });

    const { result } = renderHook(() => useKbCombineSearch('test query'));

    expect(result.current.error).toEqual(mockError);
    expect(result.current.data).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
  });

  it('should indicate loading state', () => {
    vi.mocked(useSWR).mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: true,
      isValidating: true,
      mutate: vi.fn(),
    });

    const { result } = renderHook(() => useKbCombineSearch('test query'));

    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

  it('should indicate validating state', () => {
    vi.mocked(useSWR).mockReturnValue({
      data: { data: [] },
      error: undefined,
      isLoading: false,
      isValidating: true,
      mutate: vi.fn(),
    });

    const { result } = renderHook(() => useKbCombineSearch('test query'));

    expect(result.current.isValidating).toBe(true);
    expect(result.current.isLoading).toBe(false);
  });

  it('should provide mutate function for manual revalidation', () => {
    const mockMutate = vi.fn();

    vi.mocked(useSWR).mockReturnValue({
      data: { data: [] },
      error: undefined,
      isLoading: false,
      isValidating: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useKbCombineSearch('test query'));

    expect(result.current.mutate).toBe(mockMutate);
    expect(typeof result.current.mutate).toBe('function');
  });

  it('should use the correct KB_COMBINED_PATH constant', () => {
    const mockUseSWR = vi.mocked(useSWR);
    const query = 'test query';

    renderHook(() => useKbCombineSearch(query));

    const expectedKey = [`${KB_COMBINED_PATH}/search`, query];
    expect(mockUseSWR).toHaveBeenCalledWith(expectedKey, expect.any(Function), {
      revalidateOnFocus: false,
    });

    // Verify the actual path used
    expect(expectedKey[0]).toBe('/knowledgebases/combined/search');
  });

  it('should handle query updates correctly', () => {
    const mockUseSWR = vi.mocked(useSWR);

    const { rerender } = renderHook(({ query }) => useKbCombineSearch(query), {
      initialProps: { query: 'initial query' },
    });

    expect(mockUseSWR).toHaveBeenCalledWith(
      [`${KB_COMBINED_PATH}/search`, 'initial query'],
      expect.any(Function),
      { revalidateOnFocus: false }
    );

    // Clear previous calls and update query
    vi.clearAllMocks();
    rerender({ query: 'updated query' });

    expect(mockUseSWR).toHaveBeenCalledWith(
      [`${KB_COMBINED_PATH}/search`, 'updated query'],
      expect.any(Function),
      { revalidateOnFocus: false }
    );
  });

  it('should handle empty search results', () => {
    const mockEmptyResponse = { data: [] };

    vi.mocked(useSWR).mockReturnValue({
      data: mockEmptyResponse,
      error: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
    });

    const { result } = renderHook(() => useKbCombineSearch('test query'));

    expect(result.current.data).toEqual(mockEmptyResponse);
    expect(
      (result.current.data as ISuccessListResponseWithoutPagination<SearchCombineResult>)?.data
    ).toHaveLength(0);
    expect(result.current.error).toBeUndefined();
  });
});
