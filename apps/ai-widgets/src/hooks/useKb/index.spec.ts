import { renderHook } from '@testing-library/react';
import useS<PERSON> from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { KB_PATH } from '@/constants/kb';
import { KbsAPI } from '@/services/api/kbs';
import type { Kb } from '@/types';
import { useKb, useKbByIds } from './index';

// Mock the external dependencies
vi.mock('swr');
vi.mock('@/services/api/kbs');

describe('useKb hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useKb', () => {
    it('should call useSWR with the correct key and fetcher when kbId is provided', () => {
      const mockUseSWR = vi.mocked(useSWR);
      const kbId = 'kb-123';

      renderHook(() => useKb(kbId));

      expect(mockUseSWR).toHaveBeenCalledWith([KB_PATH, kbId], expect.any(Function), {
        revalidateOnFocus: false,
      });
    });

    it('should call useSWR with false key when kbId is empty', () => {
      const mockUseSWR = vi.mocked(useSWR);

      renderHook(() => useKb(''));

      expect(mockUseSWR).toHaveBeenCalledWith(false, expect.any(Function), {
        revalidateOnFocus: false,
      });
    });

    it('should call useSWR with false key when kbId is null or undefined', () => {
      const mockUseSWR = vi.mocked(useSWR);

      // Test with null
      const { rerender } = renderHook(({ kbId }) => useKb(kbId), {
        initialProps: { kbId: null as any },
      });

      expect(mockUseSWR).toHaveBeenCalledWith(false, expect.any(Function), {
        revalidateOnFocus: false,
      });

      // Test with undefined
      rerender({ kbId: undefined as any });

      expect(mockUseSWR).toHaveBeenCalledWith(false, expect.any(Function), {
        revalidateOnFocus: false,
      });
    });

    it('should return kb data when API call is successful', async () => {
      const mockKb: Kb = {
        id: 'kb-123',
        name: 'Test Knowledge Base',
        description: 'Test KB Description',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      };

      const mockKbsAPI = vi.mocked(KbsAPI);
      mockKbsAPI.getKbById.mockResolvedValue({ data: mockKb, status: 'success' });

      vi.mocked(useSWR).mockReturnValue({
        data: mockKb,
        error: undefined,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(() => useKb('kb-123'));

      expect(result.current.data).toEqual(mockKb);
      expect(result.current.error).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle API errors', () => {
      const mockError = new Error('Failed to fetch KB');

      vi.mocked(useSWR).mockReturnValue({
        data: undefined,
        error: mockError,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(() => useKb('kb-123'));

      expect(result.current.error).toEqual(mockError);
      expect(result.current.data).toBeUndefined();
    });

    it('should indicate loading state', () => {
      vi.mocked(useSWR).mockReturnValue({
        data: undefined,
        error: undefined,
        isLoading: true,
        isValidating: true,
        mutate: vi.fn(),
      });

      const { result } = renderHook(() => useKb('kb-123'));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
      expect(result.current.error).toBeUndefined();
    });

    it('should call KbsAPI.getKbById with correct kbId when fetcher is called', async () => {
      const mockKbsAPI = vi.mocked(KbsAPI);
      const mockUseSWR = vi.mocked(useSWR);
      const kbId = 'kb-123';

      renderHook(() => useKb(kbId));

      // Get the fetcher function from the useSWR call
      const fetcherFunction = mockUseSWR.mock.calls[0][1];

      // Mock the API response
      mockKbsAPI.getKbById.mockResolvedValue({
        data: {
          id: kbId,
          name: 'Test KB',
          description: 'Test Description',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
        },
        status: 'success',
      });

      // Call the fetcher function
      const result = await fetcherFunction?.();

      expect(mockKbsAPI.getKbById).toHaveBeenCalledWith(kbId);
      expect(result).toEqual({
        id: kbId,
        name: 'Test KB',
        description: 'Test Description',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      });
    });

    it('should propagate API errors from fetcher', async () => {
      const mockKbsAPI = vi.mocked(KbsAPI);
      const mockUseSWR = vi.mocked(useSWR);
      const apiError = new Error('KB not found');

      renderHook(() => useKb('kb-123'));

      // Get the fetcher function from the useSWR call
      const fetcherFunction = mockUseSWR.mock.calls[0][1];

      // Mock the API to throw an error
      mockKbsAPI.getKbById.mockRejectedValue(apiError);

      await expect(fetcherFunction?.()).rejects.toThrow('KB not found');
    });
  });

  describe('useKbByIds', () => {
    it('should call useSWR with the correct key and fetcher when kbIds are provided', () => {
      const mockUseSWR = vi.mocked(useSWR);
      const kbIds = ['kb-123', 'kb-456'];

      renderHook(() => useKbByIds(kbIds));

      expect(mockUseSWR).toHaveBeenCalledWith(
        [
          [KB_PATH, 'kb-123'],
          [KB_PATH, 'kb-456'],
        ],
        expect.any(Function),
        {
          revalidateOnFocus: false,
        }
      );
    });

    it('should call useSWR with empty array key when kbIds is empty', () => {
      const mockUseSWR = vi.mocked(useSWR);

      renderHook(() => useKbByIds([]));

      expect(mockUseSWR).toHaveBeenCalledWith([], expect.any(Function), {
        revalidateOnFocus: false,
      });
    });

    it('should return promise settled results when API calls are successful', () => {
      const mockKbs: Kb[] = [
        {
          id: 'kb-123',
          name: 'Test KB 1',
          description: 'Test Description 1',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
        },
        {
          id: 'kb-456',
          name: 'Test KB 2',
          description: 'Test Description 2',
          createdAt: '2023-01-02T00:00:00Z',
          updatedAt: '2023-01-02T00:00:00Z',
        },
      ];

      const mockSettledResults = [
        { status: 'fulfilled', value: mockKbs[0] },
        { status: 'fulfilled', value: mockKbs[1] },
      ] as PromiseSettledResult<Kb>[];

      vi.mocked(useSWR).mockReturnValue({
        data: mockSettledResults,
        error: undefined,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(() => useKbByIds(['kb-123', 'kb-456']));

      expect(result.current.data).toEqual(mockSettledResults);
      expect(result.current.error).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle mixed success and failure results in Promise.allSettled', () => {
      const mockKb: Kb = {
        id: 'kb-123',
        name: 'Test KB 1',
        description: 'Test Description 1',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      };

      const mockSettledResults = [
        { status: 'fulfilled', value: mockKb },
        { status: 'rejected', reason: new Error('KB not found') },
      ] as PromiseSettledResult<Kb>[];

      vi.mocked(useSWR).mockReturnValue({
        data: mockSettledResults,
        error: undefined,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(() => useKbByIds(['kb-123', 'kb-456']));

      expect(result.current.data).toEqual(mockSettledResults);
      expect(result.current.data?.[0]).toEqual({
        status: 'fulfilled',
        value: mockKb,
      });
      expect(result.current.data?.[1]).toEqual({
        status: 'rejected',
        reason: expect.any(Error),
      });
    });

    it('should call KbsAPI.getKbById for each kbId when fetcher is called', async () => {
      const mockKbsAPI = vi.mocked(KbsAPI);
      const mockUseSWR = vi.mocked(useSWR);
      const kbIds = ['kb-123', 'kb-456'];

      renderHook(() => useKbByIds(kbIds));

      // Get the fetcher function from the useSWR call
      const fetcherFunction = mockUseSWR.mock.calls[0][1];

      // Mock the API responses
      mockKbsAPI.getKbById
        .mockResolvedValueOnce({
          data: {
            id: 'kb-123',
            name: 'Test KB 1',
            description: 'Test Description 1',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z',
          },
          status: 'success',
        })
        .mockResolvedValueOnce({
          data: {
            id: 'kb-456',
            name: 'Test KB 2',
            description: 'Test Description 2',
            createdAt: '2023-01-02T00:00:00Z',
            updatedAt: '2023-01-02T00:00:00Z',
          },
          status: 'success',
        });

      // Call the fetcher function
      const result = await fetcherFunction?.();

      expect(mockKbsAPI.getKbById).toHaveBeenCalledTimes(2);
      expect(mockKbsAPI.getKbById).toHaveBeenCalledWith('kb-123');
      expect(mockKbsAPI.getKbById).toHaveBeenCalledWith('kb-456');

      // Check that Promise.allSettled returns the expected structure
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
    });

    it('should handle empty kbIds array in fetcher', async () => {
      const mockUseSWR = vi.mocked(useSWR);

      renderHook(() => useKbByIds([]));

      // Get the fetcher function from the useSWR call
      const fetcherFunction = mockUseSWR.mock.calls[0][1];

      // Call the fetcher function
      const result = await fetcherFunction?.();

      expect(result).toEqual([]);
      expect(KbsAPI.getKbById).not.toHaveBeenCalled();
    });

    it('should handle API errors in Promise.allSettled without throwing', async () => {
      const mockKbsAPI = vi.mocked(KbsAPI);
      const mockUseSWR = vi.mocked(useSWR);
      const kbIds = ['kb-123', 'kb-456'];

      renderHook(() => useKbByIds(kbIds));

      // Get the fetcher function from the useSWR call
      const fetcherFunction = mockUseSWR.mock.calls[0][1];

      // Mock one success and one failure
      mockKbsAPI.getKbById
        .mockResolvedValueOnce({
          data: {
            id: 'kb-123',
            name: 'Test KB 1',
            description: 'Test Description 1',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z',
          },
          status: 'success',
        })
        .mockRejectedValueOnce(new Error('KB not found'));

      // Call the fetcher function - should not throw
      const result = await fetcherFunction?.();

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
      expect((result as PromiseSettledResult<Kb>[])[0]).toEqual({
        status: 'fulfilled',
        value: {
          id: 'kb-123',
          name: 'Test KB 1',
          description: 'Test Description 1',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
        },
      });
      expect((result as PromiseSettledResult<Kb>[])[1]).toEqual({
        status: 'rejected',
        reason: expect.any(Error),
      });
    });

    it('should indicate loading state', () => {
      vi.mocked(useSWR).mockReturnValue({
        data: undefined,
        error: undefined,
        isLoading: true,
        isValidating: true,
        mutate: vi.fn(),
      });

      const { result } = renderHook(() => useKbByIds(['kb-123', 'kb-456']));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
      expect(result.current.error).toBeUndefined();
    });

    it('should handle SWR errors', () => {
      const mockError = new Error('SWR error');

      vi.mocked(useSWR).mockReturnValue({
        data: undefined,
        error: mockError,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(() => useKbByIds(['kb-123', 'kb-456']));

      expect(result.current.error).toEqual(mockError);
      expect(result.current.data).toBeUndefined();
    });
  });

  describe('Configuration', () => {
    it('should configure useSWR with revalidateOnFocus: false for useKb', () => {
      const mockUseSWR = vi.mocked(useSWR);

      renderHook(() => useKb('kb-123'));

      const configObject = mockUseSWR.mock.calls[0][2];
      expect(configObject).toEqual({
        revalidateOnFocus: false,
      });
    });

    it('should configure useSWR with revalidateOnFocus: false for useKbByIds', () => {
      const mockUseSWR = vi.mocked(useSWR);

      renderHook(() => useKbByIds(['kb-123', 'kb-456']));

      const configObject = mockUseSWR.mock.calls[0][2];
      expect(configObject).toEqual({
        revalidateOnFocus: false,
      });
    });
  });

  describe('Key generation', () => {
    it('should generate correct SWR key for useKb with valid kbId', () => {
      const mockUseSWR = vi.mocked(useSWR);
      const kbId = 'test-kb-id';

      renderHook(() => useKb(kbId));

      const swrKey = mockUseSWR.mock.calls[0][0];
      expect(swrKey).toEqual([KB_PATH, kbId]);
    });

    it('should generate correct SWR keys for useKbByIds with multiple kbIds', () => {
      const mockUseSWR = vi.mocked(useSWR);
      const kbIds = ['kb-1', 'kb-2', 'kb-3'];

      renderHook(() => useKbByIds(kbIds));

      const swrKey = mockUseSWR.mock.calls[0][0];
      expect(swrKey).toEqual([
        [KB_PATH, 'kb-1'],
        [KB_PATH, 'kb-2'],
        [KB_PATH, 'kb-3'],
      ]);
    });
  });
});
