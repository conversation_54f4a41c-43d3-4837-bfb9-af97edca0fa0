import { useCallback, useEffect, useMemo, useRef } from 'react';
import useSWRInfinite from 'swr/infinite';
import { useChatBoxUIContext } from '@/components/ChatBoxUI/context';
import ApiService from '@/components/ChatBoxUI/services/api';
import type { ChatResponse } from '@/components/ChatBoxUI/models';
import {
  generateActionButtonClickedEvent,
  generateSelectTeamInLivechatModeEvent,
  generateSendArticleAnalyticsEvent,
  generateSubmitFormMessageEvent,
} from '@/components/ChatBoxUI/utils';
import { fixEscapeCharacters } from '../utils';
import { TemplateTypes } from '../constants';

const useFetchChatbotMessages = () => {
  const {
    setMessages,
    botId,
    urlGetChatbotHistoryMessages,
    currentConversationId,
    handleTriggerChatbotFlow,
    setActionButtonEvents,
    setSubmitFormMessageEvents,
    setIsDisabledMessageInput,
    boxId,
    setArticleAnalyticsEvents,
    setSelectedLiveChatTeamEvents,
    isErrorFetchLCConversations,
    sendForceDisconnectLivechat,
    chatBoxUIState,
  } = useChatBoxUIContext();

  const loadedMessageIdsRef = useRef<Set<string>>(new Set());

  const getKey = (pageIndex: number, previousPageData: any) => {
    if (!botId || currentConversationId) return null;

    if (pageIndex === 0) return [boxId, urlGetChatbotHistoryMessages, undefined];
    if (!previousPageData?.pagination?.next) return null;

    return [boxId, urlGetChatbotHistoryMessages, previousPageData.pagination.next];
  };

  const fetcher = async ([boxId, url, cursor]: [string, string, string?]) => {
    if (isErrorFetchLCConversations) {
      await sendForceDisconnectLivechat();
    }
    const response = await ApiService.getChatbotMessagesHistory(boxId, url, cursor);
    return response;
  };

  const { data, isLoading, setSize, isValidating } = useSWRInfinite(getKey, fetcher, {
    revalidateFirstPage: false,
    revalidateOnFocus: false,
  });

  const allMessages = useMemo(() => {
    if (!data) return [];

    const messages: ChatResponse[] = [];
    for (const page of data) {
      const responseData = page?.data?.history ?? [];
      for (const msg of responseData) {
        if (!msg.id || loadedMessageIdsRef.current.has(msg.id)) continue;

        loadedMessageIdsRef.current.add(msg.id);
        messages.push(msg);
      }
    }

    return messages;
  }, [data]);

  useEffect(() => {
    /* 
      No fetching chatbot messages if the status is connecting to livechat
      Or choosing a livechat conversation
    */
    if (!botId || currentConversationId || !data?.length) return;

    const latestPage = data[data.length - 1];
    const sessionEnded = latestPage?.data?.sessionEnded;
    /*
      sessionEnded is undefined when the chatbot is not started yet - for new user.
      If the session is ended, we should reset the messages and nextCursor.
      Trigger the chatbot to start a new session.
    */
    if (sessionEnded || sessionEnded === undefined) {
      setMessages([]);
      if (chatBoxUIState) {
        // Only trigger chatbot flow when the chatbox is shown
        handleTriggerChatbotFlow();
      }
      return;
    }

    const responseData = allMessages;

    responseData.forEach((item: ChatResponse) => {
      switch (item.type) {
        case TemplateTypes.Buttons: {
          item.from = 'user';
          const actions = item.data?.actions ?? [];
          const events = actions.map((action) => generateActionButtonClickedEvent(action.id));
          setActionButtonEvents((prev) => [...prev, ...events]);
          break;
        }
        case TemplateTypes.Form: {
          const event = generateSubmitFormMessageEvent(item.id ?? '');
          setSubmitFormMessageEvents((prev) => [...prev, event]);
          break;
        }
        case TemplateTypes.Text: {
          item.data.text = fixEscapeCharacters(item.data.text);
          break;
        }
        case TemplateTypes.QA:
        case TemplateTypes.HTML: {
          const event = generateSendArticleAnalyticsEvent(item.id ?? '');
          setArticleAnalyticsEvents((prev) => [...prev, event]);
          break;
        }
        case TemplateTypes.LiveChatTeam: {
          const teams = item?.data?.teams ?? [];
          const events = teams.map((team) => generateSelectTeamInLivechatModeEvent(team.id));
          setSelectedLiveChatTeamEvents((prev) => [...prev, ...events]);
          if (item.data?.text) {
            (item as any).type = TemplateTypes.Text;
          }
          break;
        }
        default:
          break;
      }
    });

    const lastMessage = responseData[responseData.length - 1];
    if (lastMessage) {
      const isDisabled = handleDisableMessageInput(lastMessage);
      setIsDisabledMessageInput(isDisabled);
    }

    setMessages((prev) => {
      const ids = new Set(prev.map((m) => m.id));
      const newMsgs = responseData.filter((m) => m.id && !ids.has(m.id));
      return [...newMsgs, ...prev];
    });
  }, [data, handleTriggerChatbotFlow, setMessages, setIsDisabledMessageInput, chatBoxUIState]);

  const handleDisableMessageInput = useCallback((message: ChatResponse) => {
    switch (message.type) {
      case TemplateTypes.Form:
      case TemplateTypes.LiveChatTeam:
        return true;
      case TemplateTypes.Buttons:
        /* Turn on: 'Allow user input and search intents' in buttons card => Enable the input field to search */
        return !message.data?.canSearchIntent;
      default:
        return false;
    }
  }, []);

  const handleLoadMore = useCallback(() => {
    if (isValidating) return;
    setSize((prev) => prev + 1);
  }, [setSize, isValidating]);

  return {
    isLoadingChatbotMessages: isLoading,
    handleLoadMoreChatbotMessage: handleLoadMore,
    hasMoreChatbotMessages: !!data?.[data.length - 1]?.pagination?.next,
  };
};

export default useFetchChatbotMessages;
